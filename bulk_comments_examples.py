#!/usr/bin/env python3
"""
Examples of Different Methods to Fetch ALL Comments from ALL Tickets

This script demonstrates various approaches to bulk comment retrieval:
1. Incremental Export API (fastest)
2. Batch processing (more control)
3. Filtered approaches (specific date ranges, etc.)
"""

from zendesk_bulk_comments import *
import json
from datetime import datetime, timed<PERSON><PERSON>


def example_1_incremental_all_time():
    """Example 1: Get ALL comments from the beginning of time using Incremental Export."""
    print("📋 Example 1: Incremental Export - ALL Comments (All Time)")
    print("-" * 60)
    
    try:
        # Method 1: Get everything from the beginning
        print("🔄 Fetching ALL comments from the beginning of time...")
        all_comments = fetch_all_comments_incremental(start_time=0)
        
        if all_comments:
            print(f"✅ Found {len(all_comments)} total comments!")
            
            # Show sample of first few comments
            print("\n📝 Sample of first 3 comments:")
            for i, comment in enumerate(all_comments[:3], 1):
                print(f"  {i}. Ticket {comment.get('ticket_id')}: {comment.get('body', '')[:100]}...")
            
            # Enrich and save
            users = fetch_all_users()
            user_lookup = build_user_lookup(users)
            enriched_comments = enrich_all_comments_with_users(all_comments, user_lookup)
            save_all_comments_to_files(enriched_comments, filename_prefix="all_comments_complete")
        else:
            print("❌ No comments found")
            
    except Exception as e:
        print(f"❌ Error: {e}")


def example_2_incremental_recent():
    """Example 2: Get comments from the last 30 days using Incremental Export."""
    print("\n📋 Example 2: Incremental Export - Recent Comments (Last 30 Days)")
    print("-" * 60)
    
    try:
        # Calculate timestamp for 30 days ago
        thirty_days_ago = datetime.now() - timedelta(days=30)
        start_timestamp = int(thirty_days_ago.timestamp())
        
        print(f"🔄 Fetching comments from {thirty_days_ago.strftime('%Y-%m-%d')} onwards...")
        recent_comments = fetch_all_comments_incremental(start_time=start_timestamp)
        
        if recent_comments:
            print(f"✅ Found {len(recent_comments)} comments from the last 30 days!")
            
            # Enrich and save
            users = fetch_all_users()
            user_lookup = build_user_lookup(users)
            enriched_comments = enrich_all_comments_with_users(recent_comments, user_lookup)
            save_all_comments_to_files(enriched_comments, filename_prefix="recent_comments_30days")
        else:
            print("❌ No recent comments found")
            
    except Exception as e:
        print(f"❌ Error: {e}")


def example_3_batch_processing():
    """Example 3: Get all comments using batch processing method."""
    print("\n📋 Example 3: Batch Processing - All Comments")
    print("-" * 60)
    
    try:
        print("🔄 Using batch processing method...")
        all_comments = fetch_all_comments_by_tickets()
        
        if all_comments:
            print(f"✅ Found {len(all_comments)} total comments!")
            
            # Enrich and save
            users = fetch_all_users()
            user_lookup = build_user_lookup(users)
            enriched_comments = enrich_all_comments_with_users(all_comments, user_lookup)
            save_all_comments_to_files(enriched_comments, filename_prefix="all_comments_batch")
        else:
            print("❌ No comments found")
            
    except Exception as e:
        print(f"❌ Error: {e}")


def example_4_specific_tickets():
    """Example 4: Get comments for specific tickets only."""
    print("\n📋 Example 4: Specific Tickets - Comments from Selected Tickets")
    print("-" * 60)
    
    try:
        # Load existing tickets to get some IDs
        try:
            with open("tickets_enriched.json", "r", encoding="utf-8") as f:
                tickets = json.load(f)
            
            # Get first 10 ticket IDs as example
            ticket_ids = [t['id'] for t in tickets[:10]]
            print(f"🎫 Processing {len(ticket_ids)} specific tickets: {ticket_ids}")
            
            # Fetch comments for these specific tickets
            specific_comments = fetch_all_comments_by_tickets(ticket_ids)
            
            if specific_comments:
                print(f"✅ Found {len(specific_comments)} comments from {len(ticket_ids)} tickets!")
                
                # Enrich and save
                users = fetch_all_users()
                user_lookup = build_user_lookup(users)
                enriched_comments = enrich_all_comments_with_users(specific_comments, user_lookup)
                save_all_comments_to_files(enriched_comments, filename_prefix="specific_tickets_comments")
            else:
                print("❌ No comments found for specified tickets")
                
        except FileNotFoundError:
            print("❌ tickets_enriched.json not found. Run the main notebook first.")
            
    except Exception as e:
        print(f"❌ Error: {e}")


def example_5_analyze_comments():
    """Example 5: Analyze the comments data."""
    print("\n📋 Example 5: Comment Analysis")
    print("-" * 60)
    
    try:
        # First get all comments
        print("🔄 Fetching all comments for analysis...")
        all_comments = fetch_all_comments_incremental(start_time=0)
        
        if not all_comments:
            print("❌ No comments found for analysis")
            return
        
        # Enrich with user data
        users = fetch_all_users()
        user_lookup = build_user_lookup(users)
        enriched_comments = enrich_all_comments_with_users(all_comments, user_lookup)
        
        # Analyze the data
        print(f"\n📊 Comment Analysis Results:")
        print(f"   Total comments: {len(enriched_comments)}")
        
        # Count by ticket
        ticket_comment_counts = {}
        for comment in enriched_comments:
            ticket_id = comment.get('ticket_id')
            ticket_comment_counts[ticket_id] = ticket_comment_counts.get(ticket_id, 0) + 1
        
        print(f"   Tickets with comments: {len(ticket_comment_counts)}")
        print(f"   Average comments per ticket: {len(enriched_comments) / len(ticket_comment_counts):.1f}")
        
        # Top tickets by comment count
        top_tickets = sorted(ticket_comment_counts.items(), key=lambda x: x[1], reverse=True)[:5]
        print(f"\n   🏆 Top 5 tickets by comment count:")
        for ticket_id, count in top_tickets:
            print(f"      Ticket {ticket_id}: {count} comments")
        
        # Count by author
        author_counts = {}
        for comment in enriched_comments:
            author = comment.get('author_name', 'Unknown')
            author_counts[author] = author_counts.get(author, 0) + 1
        
        top_authors = sorted(author_counts.items(), key=lambda x: x[1], reverse=True)[:5]
        print(f"\n   👥 Top 5 comment authors:")
        for author, count in top_authors:
            print(f"      {author}: {count} comments")
        
        # Public vs Private
        public_count = sum(1 for c in enriched_comments if c.get('public', False))
        private_count = len(enriched_comments) - public_count
        print(f"\n   🔓 Public comments: {public_count}")
        print(f"   🔒 Private comments: {private_count}")
        
        # Save analysis results
        analysis = {
            "total_comments": len(enriched_comments),
            "tickets_with_comments": len(ticket_comment_counts),
            "average_comments_per_ticket": len(enriched_comments) / len(ticket_comment_counts),
            "top_tickets_by_comments": top_tickets,
            "top_authors": top_authors,
            "public_comments": public_count,
            "private_comments": private_count,
            "analysis_date": datetime.now().isoformat()
        }
        
        with open("comment_analysis.json", "w", encoding="utf-8") as f:
            json.dump(analysis, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 Analysis saved to comment_analysis.json")
        
    except Exception as e:
        print(f"❌ Error during analysis: {e}")


def run_all_examples():
    """Run all examples in sequence."""
    print("🚀 Running All Bulk Comment Examples")
    print("=" * 60)
    
    examples = [
        ("Incremental Export - All Time", example_1_incremental_all_time),
        ("Incremental Export - Recent", example_2_incremental_recent),
        ("Batch Processing", example_3_batch_processing),
        ("Specific Tickets", example_4_specific_tickets),
        ("Comment Analysis", example_5_analyze_comments)
    ]
    
    for name, func in examples:
        print(f"\n🔄 Running: {name}")
        try:
            func()
            print(f"✅ Completed: {name}")
        except Exception as e:
            print(f"❌ Failed: {name} - {e}")
        
        print("\n" + "─" * 60)
    
    print("\n🎉 All examples completed!")


def interactive_examples():
    """Interactive menu for running specific examples."""
    print("🎫 Zendesk Bulk Comments - Examples Menu")
    print("=" * 50)
    
    examples = [
        ("Incremental Export - All Comments (All Time)", example_1_incremental_all_time),
        ("Incremental Export - Recent Comments (30 Days)", example_2_incremental_recent),
        ("Batch Processing - All Comments", example_3_batch_processing),
        ("Specific Tickets - Selected Comments", example_4_specific_tickets),
        ("Comment Analysis - Analyze All Data", example_5_analyze_comments),
        ("Run All Examples", run_all_examples)
    ]
    
    while True:
        print("\nChoose an example to run:")
        for i, (name, _) in enumerate(examples, 1):
            print(f"{i}. {name}")
        print(f"{len(examples) + 1}. Exit")
        
        try:
            choice = int(input(f"\nEnter your choice (1-{len(examples) + 1}): ").strip())
            
            if 1 <= choice <= len(examples):
                name, func = examples[choice - 1]
                print(f"\n🚀 Running: {name}")
                func()
            elif choice == len(examples) + 1:
                print("👋 Goodbye!")
                break
            else:
                print("❌ Invalid choice. Please try again.")
                
        except ValueError:
            print("❌ Please enter a valid number.")
        except KeyboardInterrupt:
            print("\n❌ Operation cancelled by user")
            break


if __name__ == "__main__":
    interactive_examples()
