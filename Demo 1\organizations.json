[{"url": "https://ibaset.zendesk.com/api/v2/organizations/42224668.json", "id": 42224668, "name": "Gmail", "shared_tickets": false, "shared_comments": false, "external_id": null, "created_at": "2015-04-12T21:09:15Z", "updated_at": "2016-09-23T18:47:56Z", "domain_names": ["csc.com", "gmail.com"], "details": "", "notes": "", "group_id": null, "tags": [], "organization_fields": {"last_customer_survey": null, "ftp_folder": null, "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": null, "solumina_version_currently_in_use": null, "account_owner": null, "services_owner": null, "type": null, "solumina_version_in_testing": null, "maintenance_type1": null, "support_package": null, "salesforce_id": null}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/********.json", "id": ********, "name": "Cirrus - Air - Duluth MN", "shared_tickets": true, "shared_comments": true, "external_id": null, "created_at": "2015-03-24T23:02:48Z", "updated_at": "2025-03-24T20:59:51Z", "domain_names": ["cirrusaircraft.com"], "details": "Cirrus Aircraft\n4515 Taylor Circle\nDuluth, MN  55811", "notes": "Contacts\n<PERSON>, \nInfrastructure Manager\ns<PERSON><PERSON><PERSON>@cirrusaircraft.com \n218-788-3401\n\n<PERSON>, \nApplication Administrator\n<EMAIL> \n218-788-3360\n\n<PERSON>, \nProject Lead\n<EMAIL> \n218-788-3043\n\n<PERSON>,\n Network Administrator\n<EMAIL> \n218-788-3925", "group_id": null, "tags": ["g7", "swep", "as_windows_server_2008_r2", "oracle_11g_r2", "db_linux", "java_6", "g7r5sp9", "erp_other", "support_level_product_only", "acct_regular", "has_swep"], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolders/cirrusaircraft", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0013000000ASmH9", "solumina_version_currently_in_use": "i070.6-1", "account_owner": "<PERSON>", "services_owner": "<PERSON>", "type": "Customer", "solumina_version_in_testing": null, "maintenance_type1": "Mainstream Support", "support_package": null, "salesforce_id": "0013000000ASmH9AAL"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/********.json", "id": ********, "name": "LM - Sikorsky - Stratford CT", "shared_tickets": true, "shared_comments": false, "external_id": null, "created_at": "2015-03-24T23:05:32Z", "updated_at": "2023-04-20T16:17:12Z", "domain_names": ["csc.com", "lmco.com", "sikorsky.com"], "details": "", "notes": "", "group_id": null, "tags": ["g7", "db_linux", "as_unix", "erp_sap", "con_pool_mon", "acct_marquis"], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolders/sikorsky", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0015000000HtrHB", "solumina_version_currently_in_use": "G7R5 SP7 (w/G7 SB for Win10)", "account_owner": "<PERSON>", "services_owner": "<PERSON>", "type": "Customer", "solumina_version_in_testing": null, "maintenance_type1": "Mainstream Support", "support_package": null, "salesforce_id": "0015000000HtrHBAAZ"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/********.json", "id": ********, "name": "NG - AS - Redondo Beach CA", "shared_tickets": false, "shared_comments": false, "external_id": null, "created_at": "2015-03-24T23:06:30Z", "updated_at": "2023-07-26T19:29:19Z", "domain_names": ["ngc.com"], "details": "", "notes": "", "group_id": null, "tags": ["g8r1sp4", "support_level_implementation", "acct_marquis"], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolders/northropgrummanredondo", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0013000000Cct5a", "solumina_version_currently_in_use": "G8R2 SP4 HF3 | testing SP7HF3 and HF4", "account_owner": "<PERSON>", "services_owner": "<PERSON>", "type": "Customer", "solumina_version_in_testing": null, "maintenance_type1": "Mainstream Support", "support_package": null, "salesforce_id": "0013000000Cct5aAAB"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/********.json", "id": ********, "name": "Gulfstream - Aerospace - Savannah GA", "shared_tickets": false, "shared_comments": false, "external_id": null, "created_at": "2015-03-24T23:06:54Z", "updated_at": "2023-04-13T21:06:07Z", "domain_names": ["gulfstream.com"], "details": "", "notes": "", "group_id": null, "tags": ["g5", "oracle_10_or_lower", "db_unix", "erp_sap", "g7r4sp6", "activemq_5.2.x", "support_level_product_and_configuration", "acct_marquis", "standard_support_pkg"], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolders/gulfstream", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0013000000CGAwJ", "solumina_version_currently_in_use": "G5", "account_owner": "<PERSON>", "services_owner": "<PERSON>", "type": "Customer", "solumina_version_in_testing": null, "maintenance_type1": "Mainstream Support", "support_package": "standard_support_pkg", "salesforce_id": "0013000000CGAwJAAX"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/********.json", "id": ********, "name": "LM - Aero - Pinellas Park FL", "shared_tickets": true, "shared_comments": true, "external_id": null, "created_at": "2015-03-24T23:07:36Z", "updated_at": "2024-01-10T19:27:30Z", "domain_names": ["lmco.com"], "details": "", "notes": "Pinellas contacts regarding Solumina are @Nelson, <PERSON> (US)and @Adorno, <PERSON> (US)", "group_id": null, "tags": ["g8r1sp4", "as_linux", "erp_other", "support_level_implementation", "j<PERSON>s", "acct_regular"], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolders/lmtpinellas", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0013000000ASlAV", "solumina_version_currently_in_use": "G8R2 SP4 HF6", "account_owner": "<PERSON>", "services_owner": "<PERSON>", "type": "Customer", "solumina_version_in_testing": null, "maintenance_type1": "Mainstream Support", "support_package": null, "salesforce_id": "0013000000ASlAVAA1"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/********.json", "id": ********, "name": "<PERSON>", "shared_tickets": false, "shared_comments": false, "external_id": null, "created_at": "2015-03-24T23:07:56Z", "updated_at": "2023-04-20T16:13:07Z", "domain_names": ["parker.com"], "details": "", "notes": "", "group_id": null, "tags": ["tomcat_6.x", "oracle_11g_r1", "db_unix", "activemq_5.3.x", "java_6", "mule", "support_level_implementation", "acct_marquis", "g7r6sp1"], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolders/parker", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0013000000AXbqr", "solumina_version_currently_in_use": null, "account_owner": "<PERSON>", "services_owner": "<PERSON>", "type": "Customer", "solumina_version_in_testing": null, "maintenance_type1": "Mainstream Support", "support_package": null, "salesforce_id": "0013000000AXbqrAAD"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/********.json", "id": ********, "name": "NNSA - KCNSC - Kansas City MO", "shared_tickets": true, "shared_comments": true, "external_id": null, "created_at": "2015-03-24T23:09:18Z", "updated_at": "2023-04-13T21:06:28Z", "domain_names": ["kcnsc.doe.gov", "kcp.com"], "details": "", "notes": "", "group_id": null, "tags": ["acct_regular"], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolders/kcp", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0013000000CIRZr", "solumina_version_currently_in_use": "G8R2 SP4 HF14", "account_owner": "<PERSON>", "services_owner": "<PERSON>", "type": "Customer", "solumina_version_in_testing": null, "maintenance_type1": "Mainstream Support", "support_package": null, "salesforce_id": "0013000000CIRZrAAP"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/********.json", "id": ********, "name": "NASA - MSC - Huntsville AL", "shared_tickets": false, "shared_comments": false, "external_id": null, "created_at": "2015-03-24T23:09:45Z", "updated_at": "2023-09-22T19:50:34Z", "domain_names": ["nasa.gov"], "details": "", "notes": "Use Teams, not Zoom if possible- sensitive data limitations", "group_id": null, "tags": ["mule", "support_level_product_only", "oracle_10_or_lower", "g8r1sp3", "tomcat_8.x", "acct_marquis"], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolders/nasa-marshal", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0015000000II9f4", "solumina_version_currently_in_use": "G8R2SP7", "account_owner": "<PERSON>", "services_owner": "<PERSON>", "type": "Customer", "solumina_version_in_testing": null, "maintenance_type1": "Mainstream Support", "support_package": null, "salesforce_id": "0015000000II9f4AAD"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/********.json", "id": ********, "name": "Honda - Aero - Burlington NC", "shared_tickets": true, "shared_comments": true, "external_id": null, "created_at": "2015-03-24T23:10:18Z", "updated_at": "2023-04-20T16:01:47Z", "domain_names": ["honda-aero.com"], "details": "", "notes": "", "group_id": null, "tags": ["acct_regular", "support_level_implementation"], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolders/honda-aero", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0015000000L4Y84", "solumina_version_currently_in_use": "G8R2 SP4 HF4", "account_owner": "<PERSON>", "services_owner": "<PERSON>", "type": "Customer", "solumina_version_in_testing": null, "maintenance_type1": "Mainstream Support", "support_package": null, "salesforce_id": "0015000000L4Y84AAF"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/********.json", "id": ********, "name": "<PERSON>", "shared_tickets": false, "shared_comments": false, "external_id": null, "created_at": "2015-03-24T23:12:17Z", "updated_at": "2023-04-13T21:07:04Z", "domain_names": ["hs.utc.com"], "details": "", "notes": "", "group_id": null, "tags": ["acct_regular"], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolders/hs.utc", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0013000000E3ffj", "solumina_version_currently_in_use": "G7R5, B1062 / G8R2SP4, HF5", "account_owner": null, "services_owner": null, "type": null, "solumina_version_in_testing": null, "maintenance_type1": null, "support_package": null, "salesforce_id": "0013000000E3ffjAAB"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/********.json", "id": ********, "name": "Solar Turbines - San Diego CA", "shared_tickets": true, "shared_comments": true, "external_id": null, "created_at": "2015-03-24T23:15:19Z", "updated_at": "2023-09-27T18:08:30Z", "domain_names": ["solarturbines.com"], "details": "", "notes": "", "group_id": null, "tags": ["g8r1sp2", "support_level_implementation", "acct_gateway"], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolders/solarturbines", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0013000000ASl2Q", "solumina_version_currently_in_use": "G8R2SP7HF5 Beta", "account_owner": "<PERSON>", "services_owner": "<PERSON>", "type": "Customer", "solumina_version_in_testing": null, "maintenance_type1": "Mainstream Support", "support_package": null, "salesforce_id": "0013000000ASl2QAAT"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/********.json", "id": ********, "name": "NORDAM - Tulsa OK", "shared_tickets": false, "shared_comments": false, "external_id": null, "created_at": "2015-03-24T23:15:41Z", "updated_at": "2023-04-13T20:59:54Z", "domain_names": ["nordam.com"], "details": "", "notes": "", "group_id": null, "tags": ["as_windows_server_2008_r2", "sql_server_2008_sp2", "db_windows_server_2008_r2", "webspheremq", "erp_sap", "con_pool_mon", "http_1.1", "g7", "biztalk", "g7r5sp1", "support_level_product_only", "tomcat_7.x", "java_7", "acct_regular"], "organization_fields": {"last_customer_survey": "2016-06-28T00:00:00+00:00", "ftp_folder": "/CustomerFolders/nordam", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0013000000ASl5W", "solumina_version_currently_in_use": "G7R5", "account_owner": "<PERSON>", "services_owner": "<PERSON>", "type": "Customer", "solumina_version_in_testing": null, "maintenance_type1": "Mainstream Support", "support_package": null, "salesforce_id": "0013000000ASl5WAAT"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/********.json", "id": ********, "name": "Textron - M & L Systems - Slidell LA", "shared_tickets": false, "shared_comments": false, "external_id": null, "created_at": "2015-03-24T23:17:42Z", "updated_at": "2023-09-12T18:09:11Z", "domain_names": [], "details": "", "notes": "", "group_id": null, "tags": ["acct_gateway"], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolders/textronsystems", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0015000000tC8u2", "solumina_version_currently_in_use": "G8R1 SP9 HF2", "account_owner": "<PERSON>", "services_owner": "<PERSON>", "type": "Customer", "solumina_version_in_testing": null, "maintenance_type1": "Mainstream Support", "support_package": null, "salesforce_id": "0015000000tC8u2AAC"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/********.json", "id": ********, "name": "LM - RMS", "shared_tickets": false, "shared_comments": false, "external_id": null, "created_at": "2015-03-24T23:44:02Z", "updated_at": "2023-07-31T19:24:01Z", "domain_names": ["lmcos.com"], "details": "", "notes": "", "group_id": null, "tags": ["support_level_implementation", "db_linux", "g7r5sp9", "acct_regular", "config_maint"], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolders/lmcos", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0013000000ASlAF", "solumina_version_currently_in_use": "i080.2", "account_owner": "<PERSON>", "services_owner": "<PERSON>", "type": "Customer", "solumina_version_in_testing": null, "maintenance_type1": "Solution Support", "support_package": null, "salesforce_id": "0013000000ASlAFAA1"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/********.json", "id": ********, "name": "Mercury", "shared_tickets": false, "shared_comments": false, "external_id": null, "created_at": "2015-03-24T23:45:30Z", "updated_at": "2023-04-13T21:07:39Z", "domain_names": ["mercury.com"], "details": "", "notes": "", "group_id": null, "tags": ["tomcat_6.x", "java_6", "g7r5sp7", "as_linux", "oracle_11g_r2", "db_linux", "support_level_product_only", "activemq_5.4.x", "acct_regular"], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolders/mercury", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0015000000e4BnS", "solumina_version_currently_in_use": null, "account_owner": null, "services_owner": null, "type": null, "solumina_version_in_testing": null, "maintenance_type1": null, "support_package": null, "salesforce_id": "0015000000e4BnSAAU"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/********.json", "id": ********, "name": "Teledyne - Rancho Cordova CA", "shared_tickets": true, "shared_comments": true, "external_id": null, "created_at": "2015-03-24T23:48:14Z", "updated_at": "2023-04-20T16:05:36Z", "domain_names": ["teledyne.com"], "details": "", "notes": "", "group_id": null, "tags": ["acct_regular"], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolders/teledyneis", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0015000000MWOFm", "solumina_version_currently_in_use": null, "account_owner": "<PERSON>", "services_owner": "<PERSON>", "type": "Customer", "solumina_version_in_testing": null, "maintenance_type1": "Mainstream Support", "support_package": null, "salesforce_id": "0015000000MWOFmAAP"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/********.json", "id": ********, "name": "USA-SPACEOPS", "shared_tickets": true, "shared_comments": true, "external_id": null, "created_at": "2015-03-24T23:49:07Z", "updated_at": "2024-02-08T17:51:25Z", "domain_names": ["usa-spaceops.com"], "details": "This is now Jacobs KOSC", "notes": "", "group_id": null, "tags": ["acct_regular"], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolder/usa-spaceops", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0012J00002RZnNZ", "solumina_version_currently_in_use": null, "account_owner": null, "services_owner": null, "type": null, "solumina_version_in_testing": null, "maintenance_type1": null, "support_package": null, "salesforce_id": "0012J00002RZnNZQA1"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/********.json", "id": ********, "name": "Collins - AWB - Troy OH", "shared_tickets": true, "shared_comments": false, "external_id": null, "created_at": "2015-03-24T23:49:27Z", "updated_at": "2023-04-20T16:01:04Z", "domain_names": ["utas.com"], "details": "", "notes": "", "group_id": null, "tags": ["acct_regular"], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolders/utas", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0015000000F4oRn", "solumina_version_currently_in_use": "G7R5, B1062 / G8R2 SP4 HF5", "account_owner": "<PERSON>", "services_owner": "<PERSON>", "type": "Customer through Parent", "solumina_version_in_testing": null, "maintenance_type1": null, "support_package": null, "salesforce_id": "0015000000F4oRnAAJ"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/********.json", "id": ********, "name": "Rolls-Royce - CDS - Birmingham UK", "shared_tickets": true, "shared_comments": true, "external_id": null, "created_at": "2015-03-26T22:04:29Z", "updated_at": "2023-04-14T20:26:12Z", "domain_names": ["rolls-royce.com"], "details": "", "notes": "", "group_id": null, "tags": ["as_windows_server_2008_r2", "oracle_11g_r2", "activemq_5.4.x", "con_pool_mon", "erp_other", "java_6", "support_level_product_only", "mule", "acct_noteworthy", "g8r1sp6hf1", "config_maint", "solution_support_pkg"], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolders/aeroenginecontrolrr", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0013000000ASl0m", "solumina_version_currently_in_use": "G8R2 SP3 HF9", "account_owner": "<PERSON>", "services_owner": "<PERSON>", "type": "Customer", "solumina_version_in_testing": null, "maintenance_type1": "Solution Support", "support_package": "solution_support_pkg", "salesforce_id": "0013000000ASl0mAAD"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/********.json", "id": ********, "name": "Cont Motors - AERO - Mobile AL", "shared_tickets": true, "shared_comments": true, "external_id": null, "created_at": "2015-03-24T22:59:23Z", "updated_at": "2023-04-13T21:03:43Z", "domain_names": ["continentalmotors.aero"], "details": "", "notes": "", "group_id": null, "tags": ["support_level_implementation", "swep", "tomcat_6.x", "as_windows_server_2008_r2", "g7r6sp9", "java_6", "acct_regular", "has_swep"], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolders/continentalmotors", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0013000000ASlrd", "solumina_version_currently_in_use": "G7R6 SP9", "account_owner": "<PERSON>", "services_owner": "<PERSON>", "type": "Customer", "solumina_version_in_testing": null, "maintenance_type1": "Mainstream Support", "support_package": null, "salesforce_id": "0013000000ASlrdAAD"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/********.json", "id": ********, "name": "L3 - Aeromet - Tulsa OK", "shared_tickets": false, "shared_comments": false, "external_id": null, "created_at": "2015-03-24T23:02:04Z", "updated_at": "2023-04-20T16:03:50Z", "domain_names": ["l-3com.com"], "details": "located in Aeromet", "notes": "", "group_id": null, "tags": ["acct_noteworthy"], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolders/l3aeromed", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "00150000011LyDt", "solumina_version_currently_in_use": "G8R1", "account_owner": "<PERSON>", "services_owner": "<PERSON>", "type": "Prospect", "solumina_version_in_testing": null, "maintenance_type1": null, "support_package": null, "salesforce_id": "00150000011LyDtAAK"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/********.json", "id": ********, "name": "BWXT", "shared_tickets": false, "shared_comments": false, "external_id": null, "created_at": "2015-03-24T23:04:30Z", "updated_at": "2025-04-03T19:13:29Z", "domain_names": ["bwxt.com"], "details": "", "notes": "", "group_id": null, "tags": ["acct_marquis"], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolders/bwxt", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0013000000ASl0z", "solumina_version_currently_in_use": "G5", "account_owner": "<PERSON>", "services_owner": "<PERSON>", "type": "Customer", "solumina_version_in_testing": null, "maintenance_type1": "Mainstream Support", "support_package": null, "salesforce_id": "0013000000ASl0zAAD"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/********.json", "id": ********, "name": "Pratt & Whitney East Hartford (HQ)", "shared_tickets": true, "shared_comments": true, "external_id": null, "created_at": "2015-03-24T23:06:03Z", "updated_at": "2024-07-25T17:57:16Z", "domain_names": ["prattwhitney.com", "pw.utc.com"], "details": "", "notes": "", "group_id": null, "tags": ["acct_marquis", "enterprise_license"], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolders/pw.utc", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0013000000ASl0s", "solumina_version_currently_in_use": "G8R2 SP4 HF3 / i080.2 / i110", "account_owner": "<PERSON>", "services_owner": "<PERSON>", "type": "Customer", "solumina_version_in_testing": null, "maintenance_type1": "Mainstream Support", "support_package": null, "salesforce_id": "0013000000ASl0sAAD"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/********.json", "id": ********, "name": "BAE - Nashua NH", "shared_tickets": true, "shared_comments": true, "external_id": null, "created_at": "2015-03-24T23:08:52Z", "updated_at": "2023-04-13T21:06:45Z", "domain_names": ["baesystems.com"], "details": "", "notes": "", "group_id": null, "tags": ["g7r5sp5", "java_7", "db_linux", "oracle_11g_r2", "as_linux", "activemq_5.4.x", "esb_other", "erp_oracle", "support_level_product_and_configuration", "acct_marquis", "config_maint", "solution_support_pkg"], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolders/baesystems", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0013000000CuEy8", "solumina_version_currently_in_use": "G7R5", "account_owner": "<PERSON>", "services_owner": "<PERSON>", "type": "Customer", "solumina_version_in_testing": null, "maintenance_type1": "Solution Support", "support_package": "solution_support_pkg", "salesforce_id": "0013000000CuEy8AAF"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/********.json", "id": ********, "name": "PWPS - Glastonbury CT", "shared_tickets": true, "shared_comments": false, "external_id": null, "created_at": "2015-03-24T23:17:04Z", "updated_at": "2023-04-13T21:09:12Z", "domain_names": ["pwps.com"], "details": "", "notes": "", "group_id": null, "tags": ["mule", "erp_sap", "con_pool_mon", "g7r6sp4", "sql_server_2008_sp2", "support_level_product_only", "swep", "db_windows_server_2008_r2", "tomcat_6.x", "java_6", "acct_regular", "has_swep"], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolders/pwps", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0015000000o37qS", "solumina_version_currently_in_use": "G7R5 SP7", "account_owner": "<PERSON>", "services_owner": "<PERSON>", "type": "Customer", "solumina_version_in_testing": null, "maintenance_type1": "Maintenance Suspended", "support_package": null, "salesforce_id": "0015000000o37qSAAQ"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/********.json", "id": ********, "name": "PW - CA - Lonhueuil CA", "shared_tickets": true, "shared_comments": false, "external_id": null, "created_at": "2015-03-24T23:18:48Z", "updated_at": "2023-07-31T20:03:08Z", "domain_names": ["pwc.ca"], "details": "", "notes": "", "group_id": null, "tags": ["acct_marquis"], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolders/pw.utc", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0013000000ASl13", "solumina_version_currently_in_use": "G7 / i080.2", "account_owner": null, "services_owner": null, "type": null, "solumina_version_in_testing": null, "maintenance_type1": null, "support_package": null, "salesforce_id": "0013000000ASl13AAD"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/********.json", "id": ********, "name": "deltek", "shared_tickets": true, "shared_comments": true, "external_id": null, "created_at": "2015-03-24T23:20:31Z", "updated_at": "2023-04-20T16:00:28Z", "domain_names": ["deltek.com"], "details": "", "notes": "", "group_id": null, "tags": ["support_level_implementation", "acct_regular"], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolders/deltek", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0015000000co2gC", "solumina_version_currently_in_use": null, "account_owner": null, "services_owner": null, "type": null, "solumina_version_in_testing": null, "maintenance_type1": null, "support_package": null, "salesforce_id": "0015000000co2gCAAQ"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/********.json", "id": ********, "name": "DXC Technology - System Integrator - Hartford CT", "shared_tickets": true, "shared_comments": false, "external_id": null, "created_at": "2015-04-28T17:06:10Z", "updated_at": "2023-04-20T15:59:24Z", "domain_names": ["csc.com", "dxc.com"], "details": "", "notes": "", "group_id": null, "tags": ["acct_regular"], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolders/csc", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0013000000ASm3G", "solumina_version_currently_in_use": null, "account_owner": null, "services_owner": null, "type": null, "solumina_version_in_testing": null, "maintenance_type1": null, "support_package": null, "salesforce_id": "0013000000ASm3GAAT"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/*********.json", "id": *********, "name": "Collins - UTC - Charlotte NC", "shared_tickets": true, "shared_comments": true, "external_id": null, "created_at": "2015-09-25T17:04:46Z", "updated_at": "2024-05-10T15:50:10Z", "domain_names": ["collins.com", "utas.utc.com"], "details": "", "notes": "", "group_id": null, "tags": ["tomcat_6.x", "g7r5sp7", "as_windows_server_2008_r2", "oracle_11g_r2", "java_7", "support_level_product_only", "acct_regular", "has_mes", "solution_support_pkg"], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolders/hs.utc", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": true, "plm_connector": false, "eqm": false, "ffid": "0013000000E3ffj", "solumina_version_currently_in_use": "G7R5, B1062 / G8R2 SP7 HF3", "account_owner": "<PERSON>", "services_owner": "<PERSON>", "type": "Customer", "solumina_version_in_testing": "G8R2SP7 HF5", "maintenance_type1": "Solution Support", "support_package": "solution_support_pkg", "salesforce_id": "0013000000E3ffjAAB"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/*********.json", "id": *********, "name": "MTC - Coventry UK", "shared_tickets": false, "shared_comments": false, "external_id": null, "created_at": "2015-09-25T17:08:53Z", "updated_at": "2023-04-20T16:02:21Z", "domain_names": ["the-mtc.org"], "details": "", "notes": "", "group_id": null, "tags": ["g8r1sp3", "support_level_implementation", "acct_regular"], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolders/mtc", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0015000000mydeL", "solumina_version_currently_in_use": null, "account_owner": "<PERSON>", "services_owner": null, "type": "Prospect", "solumina_version_in_testing": null, "maintenance_type1": null, "support_package": null, "salesforce_id": "0015000000mydeLAAQ"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/*********.json", "id": *********, "name": "ProNova Solutions - Pellissippi TN", "shared_tickets": false, "shared_comments": false, "external_id": null, "created_at": "2015-09-25T17:10:07Z", "updated_at": "2023-04-20T16:03:35Z", "domain_names": ["pronovasolutions.com"], "details": "", "notes": "", "group_id": null, "tags": ["acct_regular"], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolders/pnova", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0015000000yFRPc", "solumina_version_currently_in_use": null, "account_owner": null, "services_owner": null, "type": null, "solumina_version_in_testing": null, "maintenance_type1": null, "support_package": null, "salesforce_id": "0015000000yFRPcAAO"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/*********.json", "id": *********, "name": "LANL - Los Alamos NM", "shared_tickets": true, "shared_comments": true, "external_id": null, "created_at": "2015-10-02T19:56:22Z", "updated_at": "2023-05-23T15:32:28Z", "domain_names": ["lanl.gov"], "details": "", "notes": "", "group_id": null, "tags": ["support_level_implementation", "g8r1sp5", "oracle_12c", "acct_regular"], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolders/lanl", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0013000000ASlEg", "solumina_version_currently_in_use": "G8R2SP5HF1", "account_owner": "<PERSON>", "services_owner": "<PERSON>", "type": "Customer", "solumina_version_in_testing": null, "maintenance_type1": "Mainstream Support", "support_package": null, "salesforce_id": "0013000000ASlEgAAL"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/**********.json", "id": **********, "name": "L3 - ISR Systems - Waco TX", "shared_tickets": true, "shared_comments": true, "external_id": null, "created_at": "2015-12-11T18:18:48Z", "updated_at": "2023-04-13T21:02:32Z", "domain_names": ["l-3com.com"], "details": "Located in Waco", "notes": "", "group_id": null, "tags": ["acct_noteworthy"], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolders/l3waco", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0013000000ASlMf", "solumina_version_currently_in_use": "G8R2SP4HF20", "account_owner": "<PERSON>", "services_owner": "<PERSON>", "type": "Customer", "solumina_version_in_testing": null, "maintenance_type1": "Mainstream Support", "support_package": null, "salesforce_id": "0013000000ASlMfAAL"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/**********.json", "id": **********, "name": "OrbitalATK", "shared_tickets": false, "shared_comments": false, "external_id": null, "created_at": "2015-12-11T17:51:01Z", "updated_at": "2023-04-20T16:09:49Z", "domain_names": ["orbitalatk.com"], "details": "", "notes": "", "group_id": null, "tags": ["acct_regular"], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolders/atk", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0013000000ASl6m", "solumina_version_currently_in_use": null, "account_owner": null, "services_owner": null, "type": null, "solumina_version_in_testing": null, "maintenance_type1": null, "support_package": null, "salesforce_id": "0013000000ASl6mAAD"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/**********.json", "id": **********, "name": "Trividia Health - Fort Lauderdale FL", "shared_tickets": false, "shared_comments": false, "external_id": null, "created_at": "2016-01-19T01:49:40Z", "updated_at": "2023-04-20T16:02:06Z", "domain_names": ["trividiahealth.com"], "details": "", "notes": "", "group_id": null, "tags": ["acct_regular"], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolders/trividiahealth", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0015000000m2pXa", "solumina_version_currently_in_use": null, "account_owner": null, "services_owner": null, "type": null, "solumina_version_in_testing": null, "maintenance_type1": null, "support_package": null, "salesforce_id": "0015000000m2pXaAAI"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/**********.json", "id": **********, "name": "saabgroup", "shared_tickets": true, "shared_comments": false, "external_id": null, "created_at": "2016-01-20T00:43:05Z", "updated_at": "2024-10-29T19:18:54Z", "domain_names": ["saabgroup.com", "saabinc.com", "us.saabgroup.com"], "details": "", "notes": "", "group_id": null, "tags": ["g8", "tomcat_8.x", "db_windows_server_2012_r2", "webspheremq", "as_windows_server_2012_r2", "sql_server_2012_sp2", "java_8", "g8r1sp3", "support_level_implementation", "acct_regular"], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolders/saabgroup", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0012J00002PgdMI", "solumina_version_currently_in_use": "i090.3.2", "account_owner": null, "services_owner": null, "type": null, "solumina_version_in_testing": null, "maintenance_type1": null, "support_package": null, "salesforce_id": "0012J00002PgdMIQAZ"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/**********.json", "id": **********, "name": "SRNS - Aiken SC", "shared_tickets": false, "shared_comments": false, "external_id": null, "created_at": "2016-02-03T21:04:36Z", "updated_at": "2023-04-13T21:03:21Z", "domain_names": ["srs.gov"], "details": "", "notes": "", "group_id": null, "tags": ["acct_regular"], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolders/srs", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0013000000ASlQl", "solumina_version_currently_in_use": "G8R2 SP4 / G8R2i010", "account_owner": null, "services_owner": null, "type": null, "solumina_version_in_testing": null, "maintenance_type1": null, "support_package": null, "salesforce_id": "0013000000ASlQlAAL"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/**********.json", "id": **********, "name": "Versiant", "shared_tickets": true, "shared_comments": true, "external_id": null, "created_at": "2016-03-24T22:25:31Z", "updated_at": "2023-04-20T16:04:08Z", "domain_names": ["continentalmotors.aero", "versiant.com"], "details": "", "notes": "", "group_id": null, "tags": ["acct_regular"], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolders/versiant", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "00150000018Fk3l", "solumina_version_currently_in_use": null, "account_owner": null, "services_owner": null, "type": null, "solumina_version_in_testing": null, "maintenance_type1": null, "support_package": null, "salesforce_id": "00150000018Fk3lAAC"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/**********.json", "id": **********, "name": "ibasetTemp", "shared_tickets": true, "shared_comments": true, "external_id": null, "created_at": "2016-05-03T23:38:39Z", "updated_at": "2025-05-13T20:46:44Z", "domain_names": [], "details": "", "notes": "default group for new zendesk users", "group_id": null, "tags": [], "organization_fields": {"last_customer_survey": null, "ftp_folder": null, "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": null, "solumina_version_currently_in_use": null, "account_owner": null, "services_owner": null, "type": null, "solumina_version_in_testing": null, "maintenance_type1": null, "support_package": null, "salesforce_id": null}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/**********.json", "id": **********, "name": "Vacco", "shared_tickets": false, "shared_comments": false, "external_id": null, "created_at": "2016-05-05T17:55:48Z", "updated_at": "2023-04-20T16:03:04Z", "domain_names": [], "details": "", "notes": "", "group_id": null, "tags": ["acct_regular"], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolder/vacco", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0015000000PhjdB", "solumina_version_currently_in_use": null, "account_owner": null, "services_owner": null, "type": null, "solumina_version_in_testing": null, "maintenance_type1": null, "support_package": null, "salesforce_id": "0015000000PhjdBAAR"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/**********.json", "id": **********, "name": "NG - ATK-CLF - Clearfield UT", "shared_tickets": true, "shared_comments": true, "external_id": null, "created_at": "2016-07-13T17:38:03Z", "updated_at": "2023-04-13T21:02:10Z", "domain_names": ["orbitalatk.com"], "details": "", "notes": "", "group_id": null, "tags": ["db_linux", "oracle_10_or_lower", "acct_regular"], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolders/orbitalatkclf", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0013000000ASlh5", "solumina_version_currently_in_use": "G8R1 SP7 HF2 / +R1 SP9 fixes as configs", "account_owner": "<PERSON>", "services_owner": "<PERSON>", "type": "Customer", "solumina_version_in_testing": null, "maintenance_type1": "Mainstream Support", "support_package": null, "salesforce_id": "0013000000ASlh5AAD"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/**********.json", "id": **********, "name": "NG - ATK-ELK - Elkton MD", "shared_tickets": false, "shared_comments": false, "external_id": null, "created_at": "2016-07-13T17:38:23Z", "updated_at": "2023-04-13T21:04:25Z", "domain_names": ["orbitalatk.com"], "details": "", "notes": "", "group_id": null, "tags": ["g7", "acct_regular"], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolders/orbitalatkelkton", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0013000000ASmB9", "solumina_version_currently_in_use": "G7", "account_owner": "<PERSON>", "services_owner": "<PERSON>", "type": "Customer", "solumina_version_in_testing": null, "maintenance_type1": "Mainstream Support", "support_package": null, "salesforce_id": "0013000000ASmB9AAL"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/**********.json", "id": **********, "name": "NG - ATK-ABL - Rocket Center WV", "shared_tickets": true, "shared_comments": true, "external_id": null, "created_at": "2016-07-13T17:31:12Z", "updated_at": "2023-04-13T21:04:04Z", "domain_names": ["orbitalatk.com"], "details": "", "notes": "", "group_id": null, "tags": ["tomcat_5.x", "oracle_10_or_lower", "db_linux", "other", "mule", "support_level_product_only", "g7r5sp1", "acct_regular"], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolders/orbitalatkabl", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0013000000ASlUk", "solumina_version_currently_in_use": "G7R5 (w/G7 SB for Win10)", "account_owner": "<PERSON>", "services_owner": "<PERSON>", "type": "Customer", "solumina_version_in_testing": null, "maintenance_type1": "Mainstream Support", "support_package": null, "salesforce_id": "0013000000ASlUkAAL"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/**********.json", "id": **********, "name": "OrbitalATK BVL", "shared_tickets": false, "shared_comments": false, "external_id": null, "created_at": "2016-07-13T17:37:40Z", "updated_at": "2023-04-20T16:13:40Z", "domain_names": ["orbitalatk.com"], "details": "", "notes": "", "group_id": null, "tags": ["g7r1sp8", "support_level_implementation", "acct_regular"], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolder/orbitalatkblt", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0013000000AXbsX", "solumina_version_currently_in_use": null, "account_owner": null, "services_owner": null, "type": null, "solumina_version_in_testing": null, "maintenance_type1": null, "support_package": null, "salesforce_id": "0013000000AXbsXAAT"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/**********.json", "id": **********, "name": "Textron - Unmanned - Hunt Valley MD", "shared_tickets": true, "shared_comments": true, "external_id": null, "created_at": "2016-07-13T17:38:40Z", "updated_at": "2023-04-13T21:00:41Z", "domain_names": ["textronsytems.com"], "details": "", "notes": "", "group_id": null, "tags": ["g8r1sp5", "acct_gateway"], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolders/textronunmanned", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0013000000ASl8t", "solumina_version_currently_in_use": "G8R2SP6HF1", "account_owner": "<PERSON>", "services_owner": "<PERSON>", "type": "Customer", "solumina_version_in_testing": null, "maintenance_type1": "Mainstream Support", "support_package": null, "salesforce_id": "0013000000ASl8tAAD"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/**********.json", "id": **********, "name": "DeltekProduct", "shared_tickets": true, "shared_comments": true, "external_id": null, "created_at": "2016-07-19T23:03:18Z", "updated_at": "2023-04-20T16:00:50Z", "domain_names": [], "details": "", "notes": "", "group_id": null, "tags": ["support_level_implementation", "acct_regular"], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolders/deltek", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0015000000co2gC", "solumina_version_currently_in_use": null, "account_owner": null, "services_owner": null, "type": null, "solumina_version_in_testing": null, "maintenance_type1": null, "support_package": null, "salesforce_id": "0015000000co2gCAAQ"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/**********.json", "id": **********, "name": "Deltek -SRC", "shared_tickets": false, "shared_comments": false, "external_id": null, "created_at": "2016-07-29T22:49:23Z", "updated_at": "2023-04-20T16:00:10Z", "domain_names": ["deltek.com"], "details": "", "notes": "", "group_id": ************, "tags": [], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolders/deltek", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0015000000co2gC", "solumina_version_currently_in_use": null, "account_owner": null, "services_owner": null, "type": null, "solumina_version_in_testing": null, "maintenance_type1": null, "support_package": null, "salesforce_id": "0015000000co2gCAAQ"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/***********.json", "id": ***********, "name": "lmia", "shared_tickets": true, "shared_comments": true, "external_id": null, "created_at": "2016-09-26T18:52:06Z", "updated_at": "2023-04-20T16:07:23Z", "domain_names": [], "details": "", "notes": "", "group_id": null, "tags": ["acct_regular"], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolder/lmia", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0013000000ASl1m", "solumina_version_currently_in_use": null, "account_owner": null, "services_owner": null, "type": null, "solumina_version_in_testing": null, "maintenance_type1": null, "support_package": null, "salesforce_id": "0013000000ASl1mAAD"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/***********.json", "id": ***********, "name": "NASA - KSC - FL", "shared_tickets": true, "shared_comments": true, "external_id": null, "created_at": "2016-10-04T18:28:53Z", "updated_at": "2024-02-08T17:44:14Z", "domain_names": [], "details": "", "notes": "", "group_id": null, "tags": ["support_level_product_only", "acct_marquis"], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolders/nasa-ksc", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0013000000AXbqD", "solumina_version_currently_in_use": "G8R2 SP4 HF6 / i080.1", "account_owner": "<PERSON>", "services_owner": null, "type": "Suspect", "solumina_version_in_testing": null, "maintenance_type1": null, "support_package": null, "salesforce_id": "0013000000AXbqDAAT"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/***********.json", "id": ***********, "name": "Earlens - Menlo Park CA", "shared_tickets": true, "shared_comments": true, "external_id": null, "created_at": "2016-11-02T18:28:45Z", "updated_at": "2023-04-14T20:19:44Z", "domain_names": ["earlens.com"], "details": "SQL - Server", "notes": "", "group_id": null, "tags": ["acct_regular", "config_maint", "solution_support_pkg"], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolders/earlens", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "00150000018dtAA", "solumina_version_currently_in_use": "G8R1 SP6", "account_owner": "<PERSON>", "services_owner": "<PERSON>", "type": "Customer", "solumina_version_in_testing": null, "maintenance_type1": "Solution Support", "support_package": "solution_support_pkg", "salesforce_id": "00150000018dtAAAAY"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/***********.json", "id": ***********, "name": "ArianeGroup (ASL)", "shared_tickets": true, "shared_comments": false, "external_id": null, "created_at": "2017-01-23T16:23:19Z", "updated_at": "2023-04-20T16:22:32Z", "domain_names": ["ariane.group"], "details": "", "notes": "", "group_id": null, "tags": ["support_level_product_and_configuration", "acct_marquis"], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolders/arianegroup", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0015000001HxEM9", "solumina_version_currently_in_use": null, "account_owner": null, "services_owner": null, "type": null, "solumina_version_in_testing": null, "maintenance_type1": null, "support_package": null, "salesforce_id": "0015000001HxEM9AAN"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/***********.json", "id": ***********, "name": "AIM - BEC - Herstal Belgium", "shared_tickets": false, "shared_comments": false, "external_id": null, "created_at": "2017-03-02T20:19:40Z", "updated_at": "2023-04-14T20:20:28Z", "domain_names": ["bec.eu.com", "patriagroup.com"], "details": "", "notes": "", "group_id": null, "tags": ["acct_regular"], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolders/bec", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0015000001CA7IA", "solumina_version_currently_in_use": "G8R1 SP7 HF12", "account_owner": "<PERSON>", "services_owner": "<PERSON>", "type": "Customer", "solumina_version_in_testing": null, "maintenance_type1": "Mainstream Support", "support_package": null, "salesforce_id": "0015000001CA7IAAA1"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/***********.json", "id": ***********, "name": "Virgin Orbit - Long Beach CA", "shared_tickets": true, "shared_comments": true, "external_id": null, "created_at": "2017-03-02T20:17:06Z", "updated_at": "2023-04-14T20:19:03Z", "domain_names": ["virginorbit.com"], "details": "", "notes": "", "group_id": null, "tags": ["acct_regular"], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolders/vg-launcherone", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0015000000ZtV6X", "solumina_version_currently_in_use": "G8R2 SP3 HF9", "account_owner": "<PERSON>", "services_owner": "<PERSON>", "type": "Customer", "solumina_version_in_testing": null, "maintenance_type1": "Mainstream Support", "support_package": null, "salesforce_id": "0015000000ZtV6XAAV"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/***********.json", "id": ***********, "name": "Jabil - St. Petersburg FL", "shared_tickets": true, "shared_comments": true, "external_id": null, "created_at": "2017-07-20T18:55:11Z", "updated_at": "2023-04-20T15:59:08Z", "domain_names": ["jabil.com"], "details": "", "notes": "", "group_id": null, "tags": [], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolders/jabil", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0013000000ASlQn", "solumina_version_currently_in_use": "G8R2 SP1 HF2", "account_owner": "<PERSON>", "services_owner": "<PERSON>", "type": "Customer", "solumina_version_in_testing": null, "maintenance_type1": "Maintenance Suspended", "support_package": null, "salesforce_id": "0013000000ASlQnAAL"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/***********.json", "id": ***********, "name": "Cochlear - MU - Australia", "shared_tickets": true, "shared_comments": true, "external_id": null, "created_at": "2017-11-01T18:21:50Z", "updated_at": "2023-04-20T16:02:37Z", "domain_names": ["cochlear.com"], "details": "", "notes": "", "group_id": null, "tags": [], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolders/cochlear", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0015000000o1pHR", "solumina_version_currently_in_use": null, "account_owner": "iBASEt Marketing", "services_owner": "<PERSON>", "type": "Prospect", "solumina_version_in_testing": null, "maintenance_type1": null, "support_package": null, "salesforce_id": "0015000000o1pHRAAY"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/************.json", "id": ************, "name": "GDLS - Combat Systems - Sterling Heights MI", "shared_tickets": true, "shared_comments": true, "external_id": null, "created_at": "2018-04-20T17:32:16Z", "updated_at": "2023-04-20T16:15:16Z", "domain_names": ["gdls.com"], "details": "", "notes": "", "group_id": null, "tags": [], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolders/gdls", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0013000000CGpnD", "solumina_version_currently_in_use": "G8R2 SP4 HF2", "account_owner": "<PERSON>", "services_owner": "<PERSON>", "type": "Customer", "solumina_version_in_testing": "G8R2 SP4 HF26", "maintenance_type1": "Mainstream Support", "support_package": null, "salesforce_id": "0013000000CGpnDAAT"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/************.json", "id": ************, "name": "LM - AeroParts - Johnstown", "shared_tickets": false, "shared_comments": false, "external_id": null, "created_at": "2018-04-24T20:45:38Z", "updated_at": "2023-04-20T16:11:23Z", "domain_names": [], "details": "Aero Parts Inc", "notes": "Johnstown", "group_id": null, "tags": ["has_mi"], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolders/lmapi", "archiving": false, "manufacturing_intelligence": true, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0013000000ASlQY", "solumina_version_currently_in_use": "G8R2SP4, HF7", "account_owner": "<PERSON>", "services_owner": "<PERSON>", "type": "Customer", "solumina_version_in_testing": null, "maintenance_type1": "Mainstream Support", "support_package": null, "salesforce_id": "0013000000ASlQYAA1"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/************.json", "id": ************, "name": "LM - MRO - F-35 Sustainment", "shared_tickets": false, "shared_comments": false, "external_id": null, "created_at": "2018-08-17T18:16:47Z", "updated_at": "2023-04-20T16:14:28Z", "domain_names": ["lmco.com"], "details": "", "notes": "", "group_id": null, "tags": [], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolders/lmcomro", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0013000000CEgkJ", "solumina_version_currently_in_use": "G8R2 SP4 HF12", "account_owner": null, "services_owner": null, "type": null, "solumina_version_in_testing": null, "maintenance_type1": null, "support_package": null, "salesforce_id": "0013000000CEgkJAAT"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/************.json", "id": ************, "name": "Airbus - IPS - Toulouse France", "shared_tickets": true, "shared_comments": true, "external_id": null, "created_at": "2018-08-29T17:04:36Z", "updated_at": "2023-04-20T16:18:59Z", "domain_names": ["airbus.com"], "details": "", "notes": "", "group_id": null, "tags": ["config_maint", "solution_support_pkg"], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolders/airbus", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0015000000QIc12", "solumina_version_currently_in_use": "G7", "account_owner": "<PERSON>", "services_owner": "<PERSON>", "type": "Customer", "solumina_version_in_testing": null, "maintenance_type1": "Solution Support", "support_package": "solution_support_pkg", "salesforce_id": "0015000000QIc12AAD"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/************.json", "id": ************, "name": "ArianeGroup - PERM - Toulouse France", "shared_tickets": true, "shared_comments": true, "external_id": null, "created_at": "2018-08-29T17:06:26Z", "updated_at": "2023-04-14T20:28:40Z", "domain_names": ["ariane.group"], "details": "", "notes": "", "group_id": null, "tags": ["acct_marquis", "config_maint", "solution_support_pkg"], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolders/arianegroup", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0015000001HxEM9", "solumina_version_currently_in_use": "G8R1SP6", "account_owner": "<PERSON>", "services_owner": "<PERSON>", "type": "Customer", "solumina_version_in_testing": null, "maintenance_type1": "Solution Support", "support_package": "solution_support_pkg", "salesforce_id": "0015000001HxEM9AAN"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/************.json", "id": ************, "name": "SAIC - Reston VA", "shared_tickets": true, "shared_comments": true, "external_id": null, "created_at": "2018-07-17T17:16:16Z", "updated_at": "2023-07-14T22:28:32Z", "domain_names": ["saic.com"], "details": "", "notes": "", "group_id": null, "tags": ["acct_regular"], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolders/saic", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0015000001Yyo0D", "solumina_version_currently_in_use": "G8R2 SP4DLTKHF7", "account_owner": "<PERSON>", "services_owner": null, "type": "Suspect", "solumina_version_in_testing": null, "maintenance_type1": null, "support_package": null, "salesforce_id": "0015000001Yyo0DAAR"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/************.json", "id": ************, "name": "GDMS - Mission Sys - Fairfax VA", "shared_tickets": false, "shared_comments": false, "external_id": null, "created_at": "2018-12-03T18:54:46Z", "updated_at": "2023-04-20T16:16:24Z", "domain_names": ["gd-ms.com"], "details": "", "notes": "", "group_id": null, "tags": [], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolders/gdms", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0013000000CHAIA", "solumina_version_currently_in_use": null, "account_owner": null, "services_owner": null, "type": null, "solumina_version_in_testing": null, "maintenance_type1": null, "support_package": null, "salesforce_id": "0013000000CHAIAAA5"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/************.json", "id": ************, "name": "SRNS - K-Area - Aiken SC", "shared_tickets": true, "shared_comments": true, "external_id": null, "created_at": "2019-02-11T16:31:48Z", "updated_at": "2023-04-13T21:02:54Z", "domain_names": ["srs.gov"], "details": "", "notes": "", "group_id": null, "tags": ["acct_regular", "new_ui_segment", "has_ui"], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolders/srnsk", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": true, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0013000000ASlQl", "solumina_version_currently_in_use": "G8R2 SP4 / G8R2i010", "account_owner": "<PERSON>", "services_owner": "<PERSON>", "type": "Customer", "solumina_version_in_testing": null, "maintenance_type1": "Mainstream Support", "support_package": null, "salesforce_id": "0013000000ASlQlAAL"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/************.json", "id": ************, "name": "NG - AS - San Diego CA", "shared_tickets": false, "shared_comments": false, "external_id": null, "created_at": "2019-03-15T17:48:43Z", "updated_at": "2023-04-13T21:05:45Z", "domain_names": ["ngc.com"], "details": "", "notes": "", "group_id": null, "tags": [], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolders/northropgrummansandiego", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0013000000CdDCH", "solumina_version_currently_in_use": "G8R2 SP2 HF7", "account_owner": "<PERSON>", "services_owner": "<PERSON>", "type": "Customer", "solumina_version_in_testing": null, "maintenance_type1": "Mainstream Support", "support_package": null, "salesforce_id": "0013000000CdDCHAA3"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/************.json", "id": ************, "name": "Viasat - Carlsbad CA", "shared_tickets": true, "shared_comments": true, "external_id": null, "created_at": "2019-04-04T21:49:10Z", "updated_at": "2023-11-13T18:43:33Z", "domain_names": ["viasat.com"], "details": "", "notes": "", "group_id": null, "tags": [], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolders/viasat", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "001500000154Tkl", "solumina_version_currently_in_use": "G8R2 SP4 HF7", "account_owner": "<PERSON>", "services_owner": "<PERSON>", "type": "Customer", "solumina_version_in_testing": "i090 Helm Beta", "maintenance_type1": "Mainstream Support", "support_package": null, "salesforce_id": "001500000154TklAAE"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/************.json", "id": ************, "name": "LM - Aero - Fort Worth TX", "shared_tickets": false, "shared_comments": false, "external_id": null, "created_at": "2019-05-16T15:48:09Z", "updated_at": "2023-07-31T19:52:19Z", "domain_names": ["lmco.com"], "details": "", "notes": "", "group_id": null, "tags": [], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolders/lmaero", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0013000000ASlFr", "solumina_version_currently_in_use": "G8R2 SP4 HF12 /", "account_owner": "<PERSON>", "services_owner": "<PERSON>", "type": "Customer", "solumina_version_in_testing": null, "maintenance_type1": "Mainstream Support", "support_package": null, "salesforce_id": "0013000000ASlFrAAL"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/************.json", "id": ************, "name": "SSL Robotics - Pasadena CA", "shared_tickets": false, "shared_comments": false, "external_id": null, "created_at": "2019-05-16T15:50:11Z", "updated_at": "2023-04-13T19:28:46Z", "domain_names": [], "details": "", "notes": "", "group_id": null, "tags": [], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolders/sslr", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0012J00002LWOST", "solumina_version_currently_in_use": "G8R2SP4, HF4, <PERSON>NFIG CPMES", "account_owner": "<PERSON>", "services_owner": "<PERSON>", "type": "Customer", "solumina_version_in_testing": null, "maintenance_type1": "Mainstream Support", "support_package": null, "salesforce_id": "0012J00002LWOSTQA5"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/************.json", "id": ************, "name": "Teledyne - SI - Thousand Oaks CA", "shared_tickets": true, "shared_comments": true, "external_id": null, "created_at": "2019-08-09T19:41:22Z", "updated_at": "2023-04-14T20:27:25Z", "domain_names": ["teledyne.com"], "details": "", "notes": "", "group_id": null, "tags": [], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolders/teledyne", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0015000000MWOFm", "solumina_version_currently_in_use": "G8R2 SP4 HF4 (w/Deltek Connector)", "account_owner": "<PERSON>", "services_owner": "<PERSON>", "type": "Customer through Partner", "solumina_version_in_testing": null, "maintenance_type1": "Mainstream Support", "support_package": null, "salesforce_id": "0015000000MWOFmAAP"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/************.json", "id": ************, "name": "Sandia Nat Lab - Albuquerque NM", "shared_tickets": false, "shared_comments": false, "external_id": null, "created_at": "2019-08-09T21:08:25Z", "updated_at": "2025-01-16T20:25:31Z", "domain_names": ["sandia.gov"], "details": "", "notes": "", "group_id": null, "tags": [], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolders/sandia", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0013000000ASl3W", "solumina_version_currently_in_use": null, "account_owner": "<PERSON>", "services_owner": "<PERSON>", "type": "Customer", "solumina_version_in_testing": null, "maintenance_type1": null, "support_package": null, "salesforce_id": "0013000000ASl3WAAT"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/************.json", "id": ************, "name": "Dynetics - MES - Huntsville AL", "shared_tickets": true, "shared_comments": true, "external_id": null, "created_at": "2020-02-25T00:51:58Z", "updated_at": "2023-04-13T21:09:33Z", "domain_names": ["dynetics.com"], "details": "", "notes": "", "group_id": null, "tags": ["acct_regular", "has_mes"], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolders/dynetics", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": true, "plm_connector": false, "eqm": false, "ffid": "0015000000TZkSw", "solumina_version_currently_in_use": "G8R2 SP4 HF4 (w/Deltek Connector)", "account_owner": "<PERSON>", "services_owner": "<PERSON>", "type": "Customer", "solumina_version_in_testing": null, "maintenance_type1": "Mainstream Support", "support_package": null, "salesforce_id": "0015000000TZkSwAAL"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/************.json", "id": ************, "name": "ACME (Training Company)", "shared_tickets": false, "shared_comments": false, "external_id": null, "created_at": "2020-03-13T15:46:03Z", "updated_at": "2020-03-13T15:46:03Z", "domain_names": [], "details": "", "notes": "", "group_id": null, "tags": [], "organization_fields": {"last_customer_survey": null, "ftp_folder": null, "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": null, "solumina_version_currently_in_use": null, "account_owner": null, "services_owner": null, "type": null, "solumina_version_in_testing": null, "maintenance_type1": null, "support_package": null, "salesforce_id": null}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/************.json", "id": ************, "name": "Unipart Group Limited", "shared_tickets": false, "shared_comments": false, "external_id": null, "created_at": "2020-04-09T17:51:55Z", "updated_at": "2023-04-20T15:54:36Z", "domain_names": ["unipart.com"], "details": "", "notes": "", "group_id": null, "tags": ["has_mes"], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolders/unipart", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": true, "plm_connector": false, "eqm": false, "ffid": "0012J00002OlZ4e", "solumina_version_currently_in_use": null, "account_owner": null, "services_owner": null, "type": null, "solumina_version_in_testing": null, "maintenance_type1": null, "support_package": null, "salesforce_id": "0012J00002OlZ4eQAF"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/************.json", "id": ************, "name": "LMCO", "shared_tickets": false, "shared_comments": false, "external_id": null, "created_at": "2020-06-24T14:22:54Z", "updated_at": "2023-04-20T15:58:42Z", "domain_names": [], "details": "", "notes": "", "group_id": null, "tags": [], "organization_fields": {"last_customer_survey": null, "ftp_folder": null, "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0013000000ASlAC", "solumina_version_currently_in_use": null, "account_owner": null, "services_owner": null, "type": null, "solumina_version_in_testing": null, "maintenance_type1": null, "support_package": null, "salesforce_id": "0013000000ASlACAA1"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/************.json", "id": ************, "name": "D&K Engineering", "shared_tickets": false, "shared_comments": false, "external_id": null, "created_at": "2020-07-06T22:49:00Z", "updated_at": "2024-02-02T15:50:24Z", "domain_names": ["dkengineering.com", "dkengr.com"], "details": "", "notes": "", "group_id": null, "tags": ["acct_regular", "has_mes", "has_ui", "lapsed"], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolders/dkengineering", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": true, "sqa": false, "mro": false, "mes": true, "plm_connector": false, "eqm": false, "ffid": "00150000018ebKF", "solumina_version_currently_in_use": "i090", "account_owner": "<PERSON>", "services_owner": "<PERSON>", "type": "Customer", "solumina_version_in_testing": null, "maintenance_type1": "Mainstream Support", "support_package": "lapsed", "salesforce_id": "00150000018ebKFAAY"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/************.json", "id": ************, "name": "LM - Sys Integrations - Owego NY", "shared_tickets": false, "shared_comments": false, "external_id": null, "created_at": "2020-11-11T16:40:02Z", "updated_at": "2023-04-20T15:58:22Z", "domain_names": [], "details": "", "notes": "", "group_id": null, "tags": [], "organization_fields": {"last_customer_survey": null, "ftp_folder": null, "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0013000000ASl4V", "solumina_version_currently_in_use": null, "account_owner": "<PERSON>", "services_owner": "<PERSON>", "type": "Prospect", "solumina_version_in_testing": null, "maintenance_type1": null, "support_package": null, "salesforce_id": "0013000000ASl4VAAT"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/************.json", "id": ************, "name": "Hyperbat - Unipart", "shared_tickets": false, "shared_comments": false, "external_id": null, "created_at": "2020-11-11T16:46:37Z", "updated_at": "2023-07-31T19:13:36Z", "domain_names": [], "details": "", "notes": "", "group_id": null, "tags": [], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolder/hyperbat", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0012J00002W4dpQ", "solumina_version_currently_in_use": "i060.2", "account_owner": "<PERSON>", "services_owner": "<PERSON>", "type": "Prospect", "solumina_version_in_testing": null, "maintenance_type1": null, "support_package": null, "salesforce_id": "0012J00002W4dpQQAR"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/************.json", "id": ************, "name": "Purdue University", "shared_tickets": false, "shared_comments": false, "external_id": null, "created_at": "2021-02-12T17:20:35Z", "updated_at": "2023-04-20T15:56:05Z", "domain_names": ["purdue.edu"], "details": "", "notes": "", "group_id": null, "tags": [], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolder/purdueuniversity", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0012J00002RZRyT", "solumina_version_currently_in_use": null, "account_owner": "iBASEt Marketing", "services_owner": "<PERSON>", "type": "Partner", "solumina_version_in_testing": null, "maintenance_type1": null, "support_package": null, "salesforce_id": "0012J00002RZRyTQAX"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/************.json", "id": ************, "name": "Maxar Robotics", "shared_tickets": true, "shared_comments": false, "external_id": null, "created_at": "2021-01-29T23:27:46Z", "updated_at": "2024-02-26T19:22:21Z", "domain_names": ["maxar.com", "sslmda.com"], "details": "", "notes": "Maxar Robotics", "group_id": null, "tags": [], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolder/Maxar", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0015000001wFZvu", "solumina_version_currently_in_use": "G8R2SP4HF4", "account_owner": "<PERSON>", "services_owner": null, "type": "Customer", "solumina_version_in_testing": null, "maintenance_type1": "Mainstream Support", "support_package": null, "salesforce_id": "0015000001wFZvuAAG"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/************.json", "id": ************, "name": "Combitech", "shared_tickets": true, "shared_comments": false, "external_id": null, "created_at": "2021-03-04T17:22:38Z", "updated_at": "2023-04-20T15:54:57Z", "domain_names": ["combitech.com"], "details": "", "notes": "", "group_id": null, "tags": [], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolder/combitech", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0012J00002RZMHc", "solumina_version_currently_in_use": "i030.1", "account_owner": "<PERSON>", "services_owner": null, "type": "Partner", "solumina_version_in_testing": null, "maintenance_type1": null, "support_package": null, "salesforce_id": "0012J00002RZMHcQAP"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/*************.json", "id": *************, "name": "Moog Aircraft Group - Wolverhampton UK", "shared_tickets": true, "shared_comments": false, "external_id": null, "created_at": "2020-12-23T22:04:24Z", "updated_at": "2023-07-31T19:55:10Z", "domain_names": ["moog.com"], "details": "", "notes": "", "group_id": null, "tags": ["has_mi", "has_ui", "has_mes", "has_plm_connector"], "organization_fields": {"last_customer_survey": null, "ftp_folder": "moogagwolverhamptonuk", "archiving": false, "manufacturing_intelligence": true, "new_ui_customer": true, "sqa": false, "mro": false, "mes": true, "plm_connector": true, "eqm": false, "ffid": "0015000000IL5Kf", "solumina_version_currently_in_use": "i080", "account_owner": "<PERSON>", "services_owner": null, "type": "Customer through Parent", "solumina_version_in_testing": null, "maintenance_type1": null, "support_package": null, "salesforce_id": "0015000000IL5KfAAL"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/*************.json", "id": *************, "name": "Rolls-Royce - CDS - US", "shared_tickets": false, "shared_comments": false, "external_id": null, "created_at": "2021-01-07T17:52:13Z", "updated_at": "2023-04-14T20:22:14Z", "domain_names": ["rolls-royce.com"], "details": "", "notes": "", "group_id": null, "tags": [], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolder/controlsdata", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0015000000o05me", "solumina_version_currently_in_use": null, "account_owner": "<PERSON>", "services_owner": "<PERSON>", "type": "Customer", "solumina_version_in_testing": null, "maintenance_type1": "Mainstream Support", "support_package": null, "salesforce_id": "0015000000o05meAAA"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/*************.json", "id": *************, "name": "Insitu", "shared_tickets": false, "shared_comments": false, "external_id": null, "created_at": "2021-01-20T00:56:17Z", "updated_at": "2023-04-20T16:03:21Z", "domain_names": ["insitu.com"], "details": "", "notes": "", "group_id": null, "tags": [], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolder/insitu", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0015000000R4N0F", "solumina_version_currently_in_use": null, "account_owner": null, "services_owner": null, "type": null, "solumina_version_in_testing": null, "maintenance_type1": null, "support_package": null, "salesforce_id": "0015000000R4N0FAAV"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/*************.json", "id": *************, "name": "IBM", "shared_tickets": false, "shared_comments": false, "external_id": null, "created_at": "2021-02-15T22:27:51Z", "updated_at": "2023-04-20T16:23:44Z", "domain_names": ["uk.ibm.com"], "details": "", "notes": "", "group_id": null, "tags": [], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolders/ibm", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0013000000ASlKs", "solumina_version_currently_in_use": null, "account_owner": null, "services_owner": null, "type": null, "solumina_version_in_testing": null, "maintenance_type1": null, "support_package": null, "salesforce_id": "0013000000ASlKsAAL"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/*************.json", "id": *************, "name": "IBM UK", "shared_tickets": false, "shared_comments": false, "external_id": null, "created_at": "2021-03-22T18:29:46Z", "updated_at": "2021-03-22T18:29:46Z", "domain_names": [], "details": null, "notes": null, "group_id": null, "tags": [], "organization_fields": {"last_customer_survey": null, "ftp_folder": null, "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": null, "solumina_version_currently_in_use": null, "account_owner": "iBASEt Marketing", "services_owner": null, "type": "Prospect", "solumina_version_in_testing": null, "maintenance_type1": null, "support_package": null, "salesforce_id": null}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/*************.json", "id": *************, "name": "AST & Science", "shared_tickets": false, "shared_comments": false, "external_id": null, "created_at": "2021-04-29T18:21:57Z", "updated_at": "2023-07-31T18:03:46Z", "domain_names": ["ast-science.com"], "details": "", "notes": "", "group_id": null, "tags": [], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolder/ast_sciences", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0012J00002ST6dM", "solumina_version_currently_in_use": "i070.3 AWS", "account_owner": "<PERSON>", "services_owner": null, "type": "Customer", "solumina_version_in_testing": null, "maintenance_type1": "SaaS", "support_package": null, "salesforce_id": "0012J00002ST6dMQAT"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/*************.json", "id": *************, "name": "ATS Global - UK", "shared_tickets": false, "shared_comments": false, "external_id": null, "created_at": "2021-07-09T16:30:31Z", "updated_at": "2023-04-20T16:01:18Z", "domain_names": ["ats-global.com"], "details": "", "notes": "", "group_id": null, "tags": [], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolder/ats-global", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0015000000j3MbM", "solumina_version_currently_in_use": null, "account_owner": "<PERSON>", "services_owner": null, "type": "Partner", "solumina_version_in_testing": null, "maintenance_type1": null, "support_package": null, "salesforce_id": "0015000000j3MbMAAU"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/*************.json", "id": *************, "name": "HCL", "shared_tickets": true, "shared_comments": true, "external_id": null, "created_at": "2021-07-09T17:46:58Z", "updated_at": "2023-04-20T15:57:14Z", "domain_names": [], "details": "", "notes": "", "group_id": ************, "tags": [], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolder/hcl", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0012J00002LV8eW", "solumina_version_currently_in_use": null, "account_owner": null, "services_owner": null, "type": null, "solumina_version_in_testing": null, "maintenance_type1": null, "support_package": null, "salesforce_id": "0012J00002LV8eWQAT"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/*************.json", "id": *************, "name": "ibaset-internal", "shared_tickets": true, "shared_comments": true, "external_id": null, "created_at": "2021-07-28T19:07:13Z", "updated_at": "2023-09-12T18:06:20Z", "domain_names": [], "details": "", "notes": "", "group_id": null, "tags": [], "organization_fields": {"last_customer_survey": null, "ftp_folder": null, "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": null, "solumina_version_currently_in_use": null, "account_owner": null, "services_owner": null, "type": null, "solumina_version_in_testing": null, "maintenance_type1": null, "support_package": null, "salesforce_id": null}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/*************.json", "id": *************, "name": "Satellogic", "shared_tickets": true, "shared_comments": true, "external_id": null, "created_at": "2021-08-18T15:51:20Z", "updated_at": "2023-07-11T18:13:35Z", "domain_names": ["satellogic.com"], "details": "", "notes": "", "group_id": null, "tags": [], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolder/satellogic", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "001500000296iNV", "solumina_version_currently_in_use": "Prod - i070.4, Dev - i070.7", "account_owner": "<PERSON>", "services_owner": null, "type": "Customer", "solumina_version_in_testing": null, "maintenance_type1": "SaaS", "support_package": null, "salesforce_id": "001500000296iNVAAY"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/*************.json", "id": *************, "name": "PW - Asheville NC", "shared_tickets": true, "shared_comments": false, "external_id": null, "created_at": "2021-09-15T20:23:03Z", "updated_at": "2023-06-16T15:08:59Z", "domain_names": ["prattwhitney.com"], "details": "", "notes": "", "group_id": null, "tags": [], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolder/pw-asheville", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0012J00002YeUNi", "solumina_version_currently_in_use": null, "account_owner": "<PERSON>", "services_owner": null, "type": "Customer through Parent", "solumina_version_in_testing": null, "maintenance_type1": null, "support_package": null, "salesforce_id": "0012J00002YeUNiQAN"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/*************.json", "id": *************, "name": "LM - Space Systems", "shared_tickets": false, "shared_comments": false, "external_id": null, "created_at": "2021-10-19T14:00:56Z", "updated_at": "2023-07-31T19:24:44Z", "domain_names": ["lmco.com"], "details": "", "notes": "", "group_id": null, "tags": ["has_mes"], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolders/lmspace", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": true, "plm_connector": false, "eqm": false, "ffid": "0013000000ASnOC", "solumina_version_currently_in_use": "i080.1", "account_owner": "<PERSON>", "services_owner": null, "type": "Customer", "solumina_version_in_testing": null, "maintenance_type1": "Mainstream Support", "support_package": null, "salesforce_id": "0013000000ASnOCAA1"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/*************.json", "id": *************, "name": "Honda Aero", "shared_tickets": false, "shared_comments": false, "external_id": null, "created_at": "2022-02-02T17:19:47Z", "updated_at": "2022-02-02T17:19:47Z", "domain_names": [], "details": "", "notes": "", "group_id": null, "tags": [], "organization_fields": {"last_customer_survey": null, "ftp_folder": null, "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": null, "solumina_version_currently_in_use": null, "account_owner": null, "services_owner": null, "type": null, "solumina_version_in_testing": null, "maintenance_type1": null, "support_package": null, "salesforce_id": null}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/*************.json", "id": *************, "name": "Magellan Aerospace", "shared_tickets": false, "shared_comments": false, "external_id": null, "created_at": "2022-09-08T15:55:08Z", "updated_at": "2023-09-28T16:19:36Z", "domain_names": ["magellan.aero"], "details": "", "notes": "", "group_id": null, "tags": [], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolders/magellan", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0013000000ASl6Y", "solumina_version_currently_in_use": "i070.2", "account_owner": null, "services_owner": null, "type": null, "solumina_version_in_testing": null, "maintenance_type1": null, "support_package": null, "salesforce_id": "0013000000ASl6YAAT"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/*************.json", "id": *************, "name": "GE Aerospace", "shared_tickets": true, "shared_comments": true, "external_id": null, "created_at": "2022-09-27T15:48:42Z", "updated_at": "2024-03-06T16:50:34Z", "domain_names": ["ge.com"], "details": "", "notes": "", "group_id": null, "tags": [], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolder/geedisonworks", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0012J00002XCeKy", "solumina_version_currently_in_use": "i080.1", "account_owner": null, "services_owner": null, "type": null, "solumina_version_in_testing": null, "maintenance_type1": null, "support_package": null, "salesforce_id": "0012J00002XCeKyQAL"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/*************.json", "id": *************, "name": "<PERSON><PERSON>", "shared_tickets": false, "shared_comments": false, "external_id": null, "created_at": "2022-09-28T15:43:04Z", "updated_at": "2023-04-20T15:53:34Z", "domain_names": ["cyient.com"], "details": "", "notes": "SI Partner, based in India", "group_id": null, "tags": ["center_of_excellence_pkg"], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolder/cyient", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0012J00002dznQv", "solumina_version_currently_in_use": null, "account_owner": null, "services_owner": null, "type": null, "solumina_version_in_testing": null, "maintenance_type1": null, "support_package": "center_of_excellence_pkg", "salesforce_id": "0012J00002dznQvQAI"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/**************.json", "id": **************, "name": "eQ", "shared_tickets": false, "shared_comments": false, "external_id": null, "created_at": "2022-11-21T16:42:19Z", "updated_at": "2023-04-20T15:56:38Z", "domain_names": ["1eq.com"], "details": "", "notes": "", "group_id": null, "tags": [], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolders/eqtechnologic", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0012J00002W5g33", "solumina_version_currently_in_use": null, "account_owner": null, "services_owner": null, "type": null, "solumina_version_in_testing": null, "maintenance_type1": null, "support_package": null, "salesforce_id": "0012J00002W5g33QAB"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/**************.json", "id": **************, "name": "SRC Inc", "shared_tickets": false, "shared_comments": false, "external_id": null, "created_at": "2023-02-08T18:13:28Z", "updated_at": "2023-04-14T20:22:55Z", "domain_names": ["srcinc.com"], "details": "", "notes": "", "group_id": null, "tags": [], "organization_fields": {"last_customer_survey": null, "ftp_folder": "/CustomerFolders/srctecinc", "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "001500000156Kb4", "solumina_version_currently_in_use": "CPMES2", "account_owner": null, "services_owner": null, "type": null, "solumina_version_in_testing": null, "maintenance_type1": null, "support_package": null, "salesforce_id": "001500000156Kb4AAE"}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/**************.json", "id": **************, "name": "EXT-DXC", "shared_tickets": false, "shared_comments": false, "external_id": null, "created_at": "2023-03-02T00:20:53Z", "updated_at": "2023-03-02T00:20:53Z", "domain_names": [], "details": "", "notes": "", "group_id": null, "tags": [], "organization_fields": {"last_customer_survey": null, "ftp_folder": null, "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": null, "solumina_version_currently_in_use": null, "account_owner": null, "services_owner": null, "type": null, "solumina_version_in_testing": null, "maintenance_type1": null, "support_package": null, "salesforce_id": null}}, {"url": "https://ibaset.zendesk.com/api/v2/organizations/**************.json", "id": **************, "name": "Honeywell Clearwater", "shared_tickets": false, "shared_comments": false, "external_id": null, "created_at": "2023-04-04T20:42:11Z", "updated_at": "2023-07-31T19:10:18Z", "domain_names": ["honeywell.com"], "details": "", "notes": "", "group_id": null, "tags": [], "organization_fields": {"last_customer_survey": null, "ftp_folder": null, "archiving": false, "manufacturing_intelligence": false, "new_ui_customer": false, "sqa": false, "mro": false, "mes": false, "plm_connector": false, "eqm": false, "ffid": "0013000000CIAej", "solumina_version_currently_in_use": "i070.1.3", "account_owner": null, "services_owner": null, "type": null, "solumina_version_in_testing": null, "maintenance_type1": null, "support_package": null, "salesforce_id": null}}]