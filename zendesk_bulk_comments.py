#!/usr/bin/env python3
"""
Zendesk API - Bulk Comments Retrieval for ALL Tickets

This script provides multiple methods to fetch ALL comments from ALL tickets:
1. Incremental Export API (RECOMMENDED) - Most efficient for large datasets
2. Batch processing by ticket ID - More control but slower
3. Search API - For filtered results

Author: Generated for IBASET Zendesk Integration
"""

import base64
import requests
import json
import csv
import os
import time
from datetime import datetime, <PERSON><PERSON><PERSON>
from typing import Dict, Any, List, Optional

# ──────────────────────────────────────────────────────────────────────────────
# CONFIGURATION (fill in your own values)
# ──────────────────────────────────────────────────────────────────────────────
SUBDOMAIN = "ibaset"
ADMIN_EMAIL = "<EMAIL>"
API_TOKEN = "7WdLcswyMlZIZWVD1UOwinHqoj86krzRVY7emJRu"

# ──────────────────────────────────────────────────────────────────────────────
# SETUP AUTH & BASE_URL
# ──────────────────────────────────────────────────────────────────────────────
auth_str = f"{ADMIN_EMAIL}/token:{API_TOKEN}".encode()
b64_auth = base64.b64encode(auth_str).decode()
HEADERS = {
    "Authorization": f"Basic {b64_auth}",
    "Content-Type": "application/json"
}
BASE_URL = f"https://{SUBDOMAIN}.zendesk.com/api/v2"


# ──────────────────────────────────────────────────────────────────────────────
# UTILITY FUNCTIONS
# ──────────────────────────────────────────────────────────────────────────────
def zendesk_get(endpoint: str, params: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
    """Fetch all pages from a Zendesk list endpoint and return a flat list."""
    url = BASE_URL + (endpoint if endpoint.startswith("/") else "/" + endpoint)
    results: List[Dict[str, Any]] = []
    while url:
        resp = requests.get(url, headers=HEADERS, params=params)
        resp.raise_for_status()
        data = resp.json()
        # find the first list in the JSON response
        list_key = next((k for k, v in data.items() if isinstance(v, list)), None)
        if not list_key:
            break
        results.extend(data[list_key])
        url = data.get("next_page")
        params = None
    return results


def fetch_all_users() -> List[Dict[str, Any]]:
    """Fetches all users (end users + agents + admins)."""
    return zendesk_get("/users.json", params={"page[size]": 100})


def build_user_lookup(users: List[Dict[str, Any]]) -> Dict[int, Dict[str, str]]:
    """Create a map: user_id → {"name": ..., "email": ...}"""
    lookup: Dict[int, Dict[str, str]] = {}
    for u in users:
        lookup[u["id"]] = {
            "name": u.get("name", ""),
            "email": u.get("email", "")
        }
    return lookup


def write_csv(data: List[Dict[str, Any]], filename: str, columns: List[str]):
    """Write data to CSV file with specified columns."""
    with open(filename, "w", newline="", encoding="utf-8") as f:
        writer = csv.DictWriter(f, fieldnames=columns)
        writer.writeheader()
        for item in data:
            row = {col: item.get(col, "") for col in columns}
            writer.writerow(row)


# ──────────────────────────────────────────────────────────────────────────────
# METHOD 1: INCREMENTAL EXPORT API (RECOMMENDED)
# ──────────────────────────────────────────────────────────────────────────────
def fetch_all_comments_incremental(start_time: int = None) -> List[Dict[str, Any]]:
    """
    Fetch ALL comments from ALL tickets using Zendesk's Incremental Export API.
    This is the most efficient method for large datasets.
    
    Args:
        start_time (int): Unix timestamp to start from (optional, 0 = all time)
        
    Returns:
        List[Dict[str, Any]]: All comments from all tickets
    """
    if start_time is None:
        start_time = 0  # Get ALL comments from the beginning of time
    
    print(f"🔄 Starting incremental export from timestamp: {start_time}")
    print("📊 This method fetches ALL comments efficiently using Zendesk's bulk export API")
    
    url = f"{BASE_URL}/incremental/ticket_events.json"
    params = {"start_time": start_time}
    
    all_comments = []
    page_count = 0
    
    while url:
        page_count += 1
        print(f"📄 Processing page {page_count}...", end=" ")
        
        resp = requests.get(url, headers=HEADERS, params=params)
        resp.raise_for_status()
        data = resp.json()
        
        # Extract comments from ticket events
        events = data.get('ticket_events', [])
        comments_in_page = 0
        
        for event in events:
            # Look for comment events in child_events
            child_events = event.get('child_events', [])
            for child in child_events:
                if child.get('event_type') == 'Comment':
                    comment = child.copy()
                    comment['ticket_id'] = event.get('ticket_id')
                    comment['event_timestamp'] = event.get('timestamp')
                    all_comments.append(comment)
                    comments_in_page += 1
        
        print(f"Found {comments_in_page} comments")
        
        # Check for next page
        url = data.get('next_page')
        params = None  # Clear params for subsequent requests
        
        # Rate limiting - be nice to Zendesk
        time.sleep(0.1)
    
    print(f"\n✅ Incremental export complete!")
    print(f"📈 Total: {len(all_comments)} comments across {page_count} pages")
    return all_comments


# ──────────────────────────────────────────────────────────────────────────────
# METHOD 2: BATCH PROCESSING BY TICKET
# ──────────────────────────────────────────────────────────────────────────────
def fetch_all_tickets() -> List[Dict[str, Any]]:
    """Fetches all tickets in your Zendesk account (paginated)."""
    return zendesk_get("/tickets.json", params={"page[size]": 100})


def fetch_ticket_comments(ticket_id: int) -> List[Dict[str, Any]]:
    """Fetches all comments for a specific ticket."""
    return zendesk_get(f"/tickets/{ticket_id}/comments.json", params={"page[size]": 100})


def fetch_all_comments_by_tickets(ticket_ids: List[int] = None) -> List[Dict[str, Any]]:
    """
    Fetch comments for all tickets by iterating through each ticket.
    Use this method if you want more control or if incremental export doesn't work.
    
    Args:
        ticket_ids (List[int]): List of ticket IDs. If None, fetches all tickets first.
        
    Returns:
        List[Dict[str, Any]]: All comments from all specified tickets
    """
    if ticket_ids is None:
        print("📥 Fetching all tickets first...")
        tickets = fetch_all_tickets()
        ticket_ids = [t['id'] for t in tickets]
    
    print(f"🎫 Processing {len(ticket_ids)} tickets for comments...")
    print("⚠️  This method is slower but gives more control over the process")
    
    all_comments = []
    processed = 0
    errors = 0
    
    for ticket_id in ticket_ids:
        try:
            comments = fetch_ticket_comments(ticket_id)
            
            # Add ticket_id to each comment for reference
            for comment in comments:
                comment['ticket_id'] = ticket_id
            
            all_comments.extend(comments)
            processed += 1
            
            if processed % 10 == 0:
                print(f"📊 Processed {processed}/{len(ticket_ids)} tickets, found {len(all_comments)} comments so far")
            
            # Rate limiting
            time.sleep(0.1)
            
        except Exception as e:
            errors += 1
            print(f"❌ Error processing ticket {ticket_id}: {e}")
            if errors > 10:  # Stop if too many errors
                print("🛑 Too many errors, stopping...")
                break
    
    print(f"\n✅ Batch processing complete!")
    print(f"📊 Processed {processed} tickets, found {len(all_comments)} total comments")
    if errors > 0:
        print(f"⚠️ {errors} tickets had errors")
    
    return all_comments


# ──────────────────────────────────────────────────────────────────────────────
# ENRICHMENT AND EXPORT FUNCTIONS
# ──────────────────────────────────────────────────────────────────────────────
def enrich_all_comments_with_users(comments: List[Dict[str, Any]], user_lookup: Dict[int, Dict[str, str]]) -> List[Dict[str, Any]]:
    """
    Enrich all comments with user information.
    
    Args:
        comments (List[Dict[str, Any]]): List of comments
        user_lookup (Dict[int, Dict[str, str]]): User lookup dictionary
        
    Returns:
        List[Dict[str, Any]]: Enriched comments with author names and emails
    """
    print(f"👥 Enriching {len(comments)} comments with user information...")
    
    enriched_comments = []
    for comment in comments:
        enriched_comment = comment.copy()
        author_id = comment.get('author_id')
        
        if author_id and author_id in user_lookup:
            enriched_comment['author_name'] = user_lookup[author_id]['name']
            enriched_comment['author_email'] = user_lookup[author_id]['email']
        else:
            enriched_comment['author_name'] = 'Unknown'
            enriched_comment['author_email'] = 'Unknown'
        
        enriched_comments.append(enriched_comment)
    
    print("✅ User enrichment complete!")
    return enriched_comments


def save_all_comments_to_files(comments: List[Dict[str, Any]], output_dir: str = ".", filename_prefix: str = "all_comments"):
    """
    Save all comments to JSON and CSV files.
    
    Args:
        comments (List[Dict[str, Any]]): List of all comments
        output_dir (str): Directory to save files
        filename_prefix (str): Prefix for output files
    """
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Save to JSON
    json_filename = os.path.join(output_dir, f"{filename_prefix}_{timestamp}.json")
    with open(json_filename, "w", encoding="utf-8") as f:
        json.dump(comments, f, indent=2, ensure_ascii=False)
    
    # Save to CSV
    if comments:
        csv_filename = os.path.join(output_dir, f"{filename_prefix}_{timestamp}.csv")
        comment_cols = [
            "ticket_id", "id", "type", "author_id", "author_name", "author_email",
            "body", "html_body", "plain_body", "public", "created_at", "updated_at"
        ]
        write_csv(comments, csv_filename, comment_cols)
        
        print(f"\n✅ All comments saved to:")
        print(f"   📄 JSON: {json_filename}")
        print(f"   📊 CSV:  {csv_filename}")
        print(f"   📈 Total comments: {len(comments)}")
        
        return json_filename, csv_filename
    
    return None, None


# ──────────────────────────────────────────────────────────────────────────────
# MAIN EXECUTION FUNCTIONS
# ──────────────────────────────────────────────────────────────────────────────
def run_incremental_export():
    """Run the incremental export method (RECOMMENDED)."""
    print("🚀 Method 1: Incremental Export API (RECOMMENDED)")
    print("=" * 60)
    
    try:
        # Fetch all comments using incremental export
        all_comments = fetch_all_comments_incremental()
        
        if not all_comments:
            print("❌ No comments found!")
            return
        
        # Fetch users for enrichment
        print("\n👥 Fetching user data for enrichment...")
        users = fetch_all_users()
        user_lookup = build_user_lookup(users)
        
        # Enrich comments with user info
        enriched_comments = enrich_all_comments_with_users(all_comments, user_lookup)
        
        # Save to files
        save_all_comments_to_files(enriched_comments, filename_prefix="all_comments_incremental")
        
    except Exception as e:
        print(f"❌ Error during incremental export: {e}")


def run_batch_processing():
    """Run the batch processing method."""
    print("🚀 Method 2: Batch Processing by Ticket")
    print("=" * 60)
    
    try:
        # Fetch all comments by processing each ticket
        all_comments = fetch_all_comments_by_tickets()
        
        if not all_comments:
            print("❌ No comments found!")
            return
        
        # Fetch users for enrichment
        print("\n👥 Fetching user data for enrichment...")
        users = fetch_all_users()
        user_lookup = build_user_lookup(users)
        
        # Enrich comments with user info
        enriched_comments = enrich_all_comments_with_users(all_comments, user_lookup)
        
        # Save to files
        save_all_comments_to_files(enriched_comments, filename_prefix="all_comments_batch")
        
    except Exception as e:
        print(f"❌ Error during batch processing: {e}")


def interactive_menu():
    """Interactive menu for choosing the method."""
    print("🎫 Zendesk Bulk Comments Retrieval Tool")
    print("=" * 50)
    print("\nChoose a method to fetch ALL comments from ALL tickets:")
    print("\n1. 🚀 Incremental Export API (RECOMMENDED)")
    print("   - Most efficient for large datasets")
    print("   - Uses Zendesk's bulk export API")
    print("   - Faster and less API calls")
    print("\n2. 🔄 Batch Processing by Ticket")
    print("   - More control over the process")
    print("   - Slower but more detailed progress")
    print("   - Good for debugging or smaller datasets")
    print("\n3. ❌ Exit")
    
    while True:
        choice = input("\nEnter your choice (1-3): ").strip()
        
        if choice == "1":
            run_incremental_export()
            break
        elif choice == "2":
            run_batch_processing()
            break
        elif choice == "3":
            print("👋 Goodbye!")
            break
        else:
            print("❌ Invalid choice. Please enter 1, 2, or 3.")


if __name__ == "__main__":
    interactive_menu()
