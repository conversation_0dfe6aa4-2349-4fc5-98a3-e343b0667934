{"cells": [{"cell_type": "code", "execution_count": 28, "id": "eeb673c4", "metadata": {}, "outputs": [], "source": ["import base64\n", "import requests\n", "import json\n", "import csv\n", "from typing import Dict, Any, List, Optional"]}, {"cell_type": "code", "execution_count": 29, "id": "80f71418", "metadata": {}, "outputs": [], "source": ["# ──────────────────────────────────────────────────────────────────────────────\n", "# CONFIGURATION (fill in your own values)\n", "# ──────────────────────────────────────────────────────────────────────────────\n", "SUBDOMAIN   = \"ibaset\"             \n", "ADMIN_EMAIL = \"<EMAIL>\"        \n", "API_TOKEN   = \"7WdLcswyMlZIZWVD1UOwinHqoj86krzRVY7emJRu\" "]}, {"cell_type": "code", "execution_count": 30, "id": "9abf5323", "metadata": {}, "outputs": [], "source": ["# ──────────────────────────────────────────────────────────────────────────────\n", "# SETUP AUTH & BASE_URL\n", "# ──────────────────────────────────────────────────────────────────────────────\n", "auth_str = f\"{ADMIN_EMAIL}/token:{API_TOKEN}\".encode()\n", "b64_auth = base64.b64encode(auth_str).decode()\n", "HEADERS = {\n", "    \"Authorization\": f\"Basic {b64_auth}\",\n", "    \"Content-Type\": \"application/json\"\n", "}\n", "BASE_URL = f\"https://{SUBDOMAIN}.zendesk.com/api/v2\""]}, {"cell_type": "code", "execution_count": 31, "id": "46f10218", "metadata": {}, "outputs": [], "source": ["# ──────────────────────────────────────────────────────────────────────────────\n", "# GENERIC GET (with pagination)\n", "# ──────────────────────────────────────────────────────────────────────────────\n", "def zendesk_get(endpoint: str, params: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:\n", "    \"\"\"Fetch all pages from a Zendesk list endpoint and return a flat list.\"\"\"\n", "    url = BASE_URL + (endpoint if endpoint.startswith(\"/\") else \"/\" + endpoint)\n", "    results: List[Dict[str, Any]] = []\n", "    while url:\n", "        resp = requests.get(url, headers=HEADERS, params=params)\n", "        resp.raise_for_status()\n", "        data = resp.json()\n", "        # find the first list in the JSON response\n", "        list_key = next((k for k,v in data.items() if isinstance(v, list)), None)\n", "        if not list_key:\n", "            break\n", "        results.extend(data[list_key])\n", "        url = data.get(\"next_page\")\n", "        params = None\n", "    return results\n"]}, {"cell_type": "code", "execution_count": 32, "id": "f2014622", "metadata": {}, "outputs": [], "source": ["\n", "# ──────────────────────────────────────────────────────────────────────────────\n", "# FETCH FUNCTIONS\n", "# ──────────────────────────────────────────────────────────────────────────────\n", "def fetch_all_tickets() -> List[Dict[str, Any]]:\n", "    \"\"\"\n", "    Fetches all tickets in your Zendesk account (paginated).\n", "    By default, Zendesk returns 25 items/page; we can request up to 100 per page.\n", "    \"\"\"\n", "    return zendesk_get(\"/tickets.json\", params={\"page[size]\": 100})\n", "\n", "\n", "def fetch_all_users() -> List[Dict[str, Any]]:\n", "    \"\"\"\n", "    Fetches all users (end users + agents + admins).\n", "    \"\"\"\n", "    return zendesk_get(\"/users.json\", params={\"page[size]\": 100})"]}, {"cell_type": "code", "execution_count": 33, "id": "bfa7c388", "metadata": {}, "outputs": [], "source": ["# ──────────────────────────────────────────────────────────────────────────────\n", "# ENRICH TICKETS WITH USER INFO\n", "# ──────────────────────────────────────────────────────────────────────────────\n", "def build_user_lookup(users: List[Dict[str, Any]]) -> Dict[int, Dict[str,str]]:\n", "    \"\"\"\n", "    Create a map: user_id → {\"name\": ..., \"email\": ...}\n", "    \"\"\"\n", "    lookup: Dict[int, Dict[str,str]] = {}\n", "    for u in users:\n", "        lookup[u[\"id\"]] = {\n", "            \"name\":  u.get(\"name\", \"\"),\n", "            \"email\": u.get(\"email\", \"\")\n", "        }\n", "    return lookup"]}, {"cell_type": "code", "execution_count": 34, "id": "4430c32d", "metadata": {}, "outputs": [], "source": ["def enrich_tickets(tickets: List[Dict[str, Any]], \n", "                   user_lookup: Dict[int, Dict[str,str]]) -> List[Dict[str, Any]]:\n", "    \"\"\"\n", "    For each ticket, add requester_name/email, submitter_name/email, assignee_name/email\n", "    (if those IDs exist).\n", "    \"\"\"\n", "    enriched = []\n", "    for t in tickets:\n", "        e = t.copy()\n", "        for field in (\"requester_id\", \"submitter_id\", \"assignee_id\"):\n", "            uid = t.get(field)\n", "            if uid and uid in user_lookup:\n", "                e[field.replace(\"_id\",\"_name\")]  = user_lookup[uid][\"name\"]\n", "                e[field.replace(\"_id\",\"_email\")] = user_lookup[uid][\"email\"]\n", "        enriched.append(e)\n", "    return enriched"]}, {"cell_type": "code", "execution_count": 35, "id": "8587a66e", "metadata": {}, "outputs": [], "source": ["# ──────────────────────────────────────────────────────────────────────────────\n", "# CSV WRITER\n", "# ──────────────────────────────────────────────────────────────────────────────\n", "def write_csv(data: List[Dict[str,Any]], filename: str, columns: List[str]):\n", "    with open(filename, \"w\", newline=\"\", encoding=\"utf-8\") as f:\n", "        writer = csv.DictWriter(f, fieldnames=columns)\n", "        writer.writeheader()\n", "        for item in data:\n", "            row = {col: item.get(col, \"\") for col in columns}\n", "            writer.writerow(row)"]}, {"cell_type": "code", "execution_count": 36, "id": "17e3cfe0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ tickets_enriched.json and tickets_enriched.csv written!\n"]}], "source": ["# ──────────────────────────────────────────────────────────────────────────────\n", "# MAIN DEMO\n", "# ──────────────────────────────────────────────────────────────────────────────\n", "if __name__ == \"__main__\":\n", "    # 1. Fetch all data\n", "    users   = fetch_all_users()\n", "    tickets = fetch_all_tickets()\n", "\n", "    # 2. Build lookup and enrich tickets\n", "    user_lookup    = build_user_lookup(users)\n", "    enriched_tix   = enrich_tickets(tickets, user_lookup)\n", "\n", "    # 3. <PERSON><PERSON> enriched tickets to JSON\n", "    with open(\"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dhruvi\\\\IBASET\\\\Work\\\\Zendesk API Data\\\\tickets_enriched.json\", \"w\", encoding=\"utf-8\") as f:\n", "        json.dump(enriched_tix, f, indent=2, ensure_ascii=False)\n", "\n", "    # 4. (Optional) write to CSV\n", "    ticket_cols = [\n", "        \"id\", \"subject\", \"status\", \"priority\",\n", "        \"requester_id\", \"requester_name\", \"requester_email\",\n", "        \"submitter_id\", \"submitter_name\", \"submitter_email\",\n", "        \"assignee_id\",  \"assignee_name\",  \"assignee_email\",\n", "        \"created_at\", \"updated_at\"\n", "    ]\n", "    write_csv(enriched_tix, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dhruvi\\\\IBASET\\\\Work\\\\Zendesk API Data\\\\tickets_enriched.csv\", ticket_cols)\n", "\n", "    print(\"✅ tickets_enriched.json and tickets_enriched.csv written!\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.0"}}, "nbformat": 4, "nbformat_minor": 5}