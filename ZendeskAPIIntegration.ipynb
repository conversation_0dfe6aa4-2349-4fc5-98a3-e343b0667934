import base64
import requests
import json
import csv
from typing import Dict, Any, List, Optional

# ──────────────────────────────────────────────────────────────────────────────
# CONFIGURATION (fill in your own values)
# ──────────────────────────────────────────────────────────────────────────────
SUBDOMAIN   = "ibaset"             
ADMIN_EMAIL = "<EMAIL>"        
API_TOKEN   = "7WdLcswyMlZIZWVD1UOwinHqoj86krzRVY7emJRu" 

# ──────────────────────────────────────────────────────────────────────────────
# SETUP AUTH & BASE_URL
# ──────────────────────────────────────────────────────────────────────────────
auth_str = f"{ADMIN_EMAIL}/token:{API_TOKEN}".encode()
b64_auth = base64.b64encode(auth_str).decode()
HEADERS = {
    "Authorization": f"Basic {b64_auth}",
    "Content-Type": "application/json"
}
BASE_URL = f"https://{SUBDOMAIN}.zendesk.com/api/v2"

# ──────────────────────────────────────────────────────────────────────────────
# GENERIC GET (with pagination)
# ──────────────────────────────────────────────────────────────────────────────
def zendesk_get(endpoint: str, params: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
    """Fetch all pages from a Zendesk list endpoint and return a flat list."""
    url = BASE_URL + (endpoint if endpoint.startswith("/") else "/" + endpoint)
    results: List[Dict[str, Any]] = []
    while url:
        resp = requests.get(url, headers=HEADERS, params=params)
        resp.raise_for_status()
        data = resp.json()
        # find the first list in the JSON response
        list_key = next((k for k,v in data.items() if isinstance(v, list)), None)
        if not list_key:
            break
        results.extend(data[list_key])
        url = data.get("next_page")
        params = None
    return results



# ──────────────────────────────────────────────────────────────────────────────
# FETCH FUNCTIONS
# ──────────────────────────────────────────────────────────────────────────────
def fetch_all_tickets() -> List[Dict[str, Any]]:
    """
    Fetches all tickets in your Zendesk account (paginated).
    By default, Zendesk returns 25 items/page; we can request up to 100 per page.
    """
    return zendesk_get("/tickets.json", params={"page[size]": 100})


def fetch_all_users() -> List[Dict[str, Any]]:
    """
    Fetches all users (end users + agents + admins).
    """
    return zendesk_get("/users.json", params={"page[size]": 100})

# ──────────────────────────────────────────────────────────────────────────────
# ENRICH TICKETS WITH USER INFO
# ──────────────────────────────────────────────────────────────────────────────
def build_user_lookup(users: List[Dict[str, Any]]) -> Dict[int, Dict[str,str]]:
    """
    Create a map: user_id → {"name": ..., "email": ...}
    """
    lookup: Dict[int, Dict[str,str]] = {}
    for u in users:
        lookup[u["id"]] = {
            "name":  u.get("name", ""),
            "email": u.get("email", "")
        }
    return lookup

def enrich_tickets(tickets: List[Dict[str, Any]], 
                   user_lookup: Dict[int, Dict[str,str]]) -> List[Dict[str, Any]]:
    """
    For each ticket, add requester_name/email, submitter_name/email, assignee_name/email
    (if those IDs exist).
    """
    enriched = []
    for t in tickets:
        e = t.copy()
        for field in ("requester_id", "submitter_id", "assignee_id"):
            uid = t.get(field)
            if uid and uid in user_lookup:
                e[field.replace("_id","_name")]  = user_lookup[uid]["name"]
                e[field.replace("_id","_email")] = user_lookup[uid]["email"]
        enriched.append(e)
    return enriched

# ──────────────────────────────────────────────────────────────────────────────
# CSV WRITER
# ──────────────────────────────────────────────────────────────────────────────
def write_csv(data: List[Dict[str,Any]], filename: str, columns: List[str]):
    with open(filename, "w", newline="", encoding="utf-8") as f:
        writer = csv.DictWriter(f, fieldnames=columns)
        writer.writeheader()
        for item in data:
            row = {col: item.get(col, "") for col in columns}
            writer.writerow(row)

# ──────────────────────────────────────────────────────────────────────────────
# MAIN DEMO
# ──────────────────────────────────────────────────────────────────────────────
if __name__ == "__main__":
    # 1. Fetch all data
    users   = fetch_all_users()
    tickets = fetch_all_tickets()

    # 2. Build lookup and enrich tickets
    user_lookup    = build_user_lookup(users)
    enriched_tix   = enrich_tickets(tickets, user_lookup)

    # 3. Dump enriched tickets to JSON
    with open("C:\\Users\\<USER>\\Documents\\Dhruvi\\IBASET\\Work\\Zendesk API Data\\tickets_enriched.json", "w", encoding="utf-8") as f:
        json.dump(enriched_tix, f, indent=2, ensure_ascii=False)

    # 4. (Optional) write to CSV
    ticket_cols = [
        "id", "subject", "status", "priority",
        "requester_id", "requester_name", "requester_email",
        "submitter_id", "submitter_name", "submitter_email",
        "assignee_id",  "assignee_name",  "assignee_email",
        "created_at", "updated_at"
    ]
    write_csv(enriched_tix, "C:\\Users\\<USER>\\Documents\\Dhruvi\\IBASET\\Work\\Zendesk API Data\\tickets_enriched.csv", ticket_cols)

    print("✅ tickets_enriched.json and tickets_enriched.csv written!")