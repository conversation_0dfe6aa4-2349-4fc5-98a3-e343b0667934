id,subject,status,priority,requester_id,created_at
7842,error exporting UDV from System Manager,pending,low,3393385537,2021-04-05T15:03:45Z
8391,RE: You Support Request [#7998] has Been Solved: Improve the Attach Documents Action on Parts,hold,low,388816307113,2021-06-28T20:51:29Z
8458,Incorporate multiple Work Order Alterations into Process Plan Revisions ,closed,normal,433121299,2021-07-07T22:19:36Z
8512,Work Center users lookup,closed,low,405669020733,2021-07-16T17:18:17Z
8525,Airworthiness - Moog require process to mark a WO Unit as airworthy and API to inform external application,closed,high,949689498,2021-07-19T22:01:01Z
8625,Not creating hold when installing unfinished subassembly ,closed,low,**********,2021-07-30T15:44:59Z
8672,"Display Communication as AUTHOR never works, always goes to ViewRecipient",hold,urgent,3493480208,2021-08-04T20:30:47Z
8880,Issue with rejecting same Ref Des on Lower level Comp,closed,low,395226442033,2021-09-03T14:17:09Z
8995,EX - JIRA AST1-403 - Dynamic Label/Report Selection + Printing - iSeries WebUI Feature Missing,closed,high,942437537,2021-09-20T22:47:46Z
9205,Unit Work History Report missing data,closed,low,1515812256601,2021-10-15T21:09:35Z
9207,Tooling Functionality Enhancements,closed,normal,433121299,2021-10-15T22:02:07Z
9223,WO Operation Complete: Label Print event is not being fired at Operation Complete. It is being fired at Serial-Oper completion,closed,high,949689498,2021-10-19T17:42:13Z
9659,Selecting MRO Plan in R_WO_ISSUE,closed,high,433121299,2021-12-08T21:27:40Z
9788,MIR5 Technical Issues & Query - MOOG,closed,normal,26132880348,2021-12-20T16:41:09Z
9997,Alteration Propagation Target Orders List,hold,normal,1506163332741,2022-01-21T21:17:01Z
10017,Enhancement request for find and replace capability,open,low,421955095293,2022-01-25T21:21:57Z
10065,Enhancement request,open,low,421955095293,2022-02-01T02:58:14Z
10112,Automatic Decision Nodes,closed,normal,433121299,2022-02-05T13:28:08Z
10121,Plan Import: Remove call to open Import log screen after file is imported.,hold,normal,949689498,2022-02-08T00:04:38Z
10126,Operation Overlap Skip qty's,open,low,395226442033,2022-02-08T12:31:31Z
10266,Some plans giving false error Part is no Longer on BOM and Part Rev is not editable,hold,urgent,12297504381203,2022-02-23T15:36:32Z
10295,"LMAERO-639 Process Plan - Part Data Collection, Next Higher Assembly attribute, must be defined as a FK ",pending,normal,401846981234,2022-02-24T23:04:10Z
10355,Re: Has scientific notation been delivered as a UOM yet?,hold,urgent,3393385537,2022-03-03T18:40:44Z
10455,Reopen Skipped Buyoffs ,hold,low,362307122367,2022-03-16T15:42:16Z
10469,MES 2.0 - NGC - BOM Line Number switches to N/A after 2.0,hold,urgent,12297504381203,2022-03-17T18:51:08Z
10670,Jira PWAVILNC-942 - Provide the ability change Discrepancy Item Type while it is in DISC_INITIATION status,hold,normal,1920422143365,2022-04-07T13:57:10Z
10671,Jira PWAVILNC-943 - Fabrication/Modification work processes need to Progress/Assign Traceability Data and send back to ERP ,open,normal,1920422143365,2022-04-07T14:14:48Z
10672,Jira PWAVILNC-944 - Fabrication/Modification work processes need to Progress/Assign Traceability Data and send back to ERP,hold,normal,1920422143365,2022-04-07T14:26:58Z
10674,Jira PWAVILNC-945- Provide execution limit/count on Return Nodes with override capability ,hold,normal,1920422143365,2022-04-07T15:05:30Z
10675,Jira PWAVILNC-946 - Provide Operator Certification at the Work Order Operation - Work Center level,open,normal,1920422143365,2022-04-07T15:13:11Z
10676,"Jira PWAVILNC-947 - Provide the ability to Request a Non-Conformance number from ERP, if ERP is the system of record for Quality",open,normal,1920422143365,2022-04-07T15:27:52Z
10677,Jira  PWAVILNC-948- Provide the ability to receive or send the Quality Engineer's Disposition and metadata,open,normal,1920422143365,2022-04-07T15:33:38Z
10704,Alternate Certificate Performance (G8R2SP6),hold,urgent,4111048127,2022-04-11T15:46:16Z
10749,Can an alert be set up when operation is in queue for a department/workcenter? ,open,low,385560479374,2022-04-14T16:38:52Z
11077,Work Center (Location Sync) message fails to update MES,hold,urgent,12297504381203,2022-05-24T20:47:33Z
11078,"Error of: Buyoff, Data Collection, Tool, Part or Illustration must be filled in before block is closed. MES",hold,urgent,12297504381203,2022-05-24T21:13:24Z
11096,JIRA PWAVILNC-975 - Provide the capability to identify Deviations/Waivers on a DI and provide visual for all outstanding Deviations/Waivers,hold,high,942437537,2022-05-25T22:09:58Z
11133,Cannot attach an MBOM to a plan in PLG_REVIEW queue,hold,high,365557410973,2022-06-01T12:02:56Z
11300,"Add ""Must Use Issued Parts"" Flag to the Step Level",closed,urgent,22425823887,2022-06-21T20:24:53Z
11359,"JIRA PWAVILNC-992 - Provide the capability to progress Operations, along with entire Process Plans as Development Plans/Operations",open,high,942437537,2022-06-28T16:05:02Z
11441,QA inspection buyoff error,closed,high,365062593634,2022-07-08T18:12:50Z
11541,Copying header & footer during process planning,open,normal,1521985312182,2022-07-21T21:00:50Z
11585,LMAERO-812 - CR108 - IN_SDK: Need capability to call the doc vault service for document retrieval (Use Case #1),open,urgent,415948105853,2022-07-28T15:58:17Z
11592,LMAERO-814 - CR116 - IN_SDK: Configurable buttons on WebUI - 3 system wide buttons,open,urgent,415948105853,2022-07-28T19:45:05Z
11594,LMAERO-816 - CR118 - IN_SDK: INI and UDVLIB availability,open,urgent,903591718,2022-07-28T19:59:06Z
11619,Exclude part removmals for parts that are removed and Replaced by the same part from work order revision messages,hold,high,942534407,2022-07-29T23:58:26Z
11678,WebUI : NTC Panel - Set default value for parametric data collection on some specific condition based on UOM:  Any way to configure using SDK?,hold,high,970739568,2022-08-04T10:20:44Z
11679,Display UCF fields on Flyout panel using configurator tool : iSeries Screen Configuration ,hold,normal,970739568,2022-08-04T10:56:52Z
11689,Break down of workflows into smaller groups enhancement request,hold,normal,1530362946561,2022-08-04T19:51:22Z
11690,Operation Revision or Comparison Screen enhancement request,hold,normal,1530362946561,2022-08-04T19:57:11Z
11716,SDK/Screen configuration Enhancement: Need approach in webUI where we can configure new command button to fulfil any customer specific requirement,hold,normal,970739568,2022-08-10T09:07:55Z
11780,LMAERO: Error when open object link created by API in Solumina WinClient,closed,high,903591718,2022-08-16T20:10:21Z
11867,<EMAIL>,open,normal,1521600915862,2022-08-25T19:08:25Z
11886,"MES 2.0 - NGC - In MES 1.0 deleting Data Collects or Buyoffs during Work Order Alteration sometimes results in ""phantom"" remnants.  Need script to cle",hold,urgent,12297504381203,2022-08-29T18:55:52Z
11951,"Cannot Collect Component Part Number - ""This order has a status of Complete/Closed and cannot be modified""",hold,low,1919701294465,2022-09-02T00:35:22Z
11971,Re: FW: License Request for Maxar TC Connector,closed,normal,1516513477622,2022-09-07T13:55:19Z
11983,How can we flag MOs that need action after PCR is approved?,open,low,1515812256601,2022-09-07T21:02:51Z
12026,Enhancement request: Enable Kubernetes secret encryption for iSeries i070,open,high,7602966238483,2022-09-12T19:34:15Z
12068,JIRA - PWAVILNC-1013 - Parameter D/C UOM Types not linking to Variable Name (cannot get Inspection Item),open,urgent,942437537,2022-09-15T17:20:39Z
12109,MES 2.0 - NGC - Printing issues,pending,urgent,12297504381203,2022-09-21T15:50:18Z
12113,Launch Native Applications from Link and Menu Action,open,low,379562026194,2022-09-21T18:00:52Z
12173,Re: Re: Search List,pending,high,**********,2022-09-28T19:47:08Z
12293,Not able to configure fields as dependent field(without configure lookup),open,low,**********,2022-10-12T12:34:26Z
12336,BOM to Plan Compare ,closed,high,401846981234,2022-10-17T22:11:46Z
12344,Relinking an OP has a product bug ,closed,low,376442830234,2022-10-18T17:47:51Z
12390,Forced Credentials on Read Acknowledge,hold,normal,401846981234,2022-10-24T22:58:09Z
12429,Configure Hold types,pending,normal,430101921733,2022-10-27T12:12:54Z
12436,"WebUI - In an operation, the header status appears active even tough the header buy off is complete ",solved,low,430101921733,2022-10-27T14:29:30Z
12444,"Master Data import error, from CSV and Application Server",hold,high,1918174620645,2022-10-27T23:51:14Z
12530,BOM in authoring visible in BOR for authored plan in i080,hold,normal,1048768228,2022-11-06T19:40:52Z
12533,WebUI button color changes/inconsistencies i080,open,normal,1048768228,2022-11-06T19:46:31Z
12571,Bug 7987,hold,normal,7167350652051,2022-11-09T18:37:15Z
12572,Re: Part Attribute Overrides ,open,normal,942534407,2022-11-09T19:07:18Z
12622,LMAERO - DR380 - Provide an option to bypass uninstall for Solumina Browser and System Mgr [G8 and iSeries],open,low,7763950366867,2022-11-15T16:36:17Z
12629,Shop floor Homepage and Supervisor Homepage,open,normal,367155043314,2022-11-15T18:49:25Z
12698,"Greenville ""GSO Plan"" Header Update",closed,low,390510724113,2022-11-23T16:56:42Z
12703,Unit of Measure Entry Screen Does not enforce Formatting,open,low,1530362946561,2022-11-23T21:48:40Z
12719,Step Headers Swapped,hold,urgent,12297504381203,2022-11-28T15:37:16Z
12726,MES 2.0 - NGC - Cannot open alteration on order (Continuation of 10276),hold,urgent,12297504381203,2022-11-28T19:15:57Z
12871,Skip function in a Data Collection,hold,low,367155043314,2022-12-15T17:40:45Z
12892,Production Control - Work Center Management - Dispatch List,hold,low,403658552494,2022-12-19T15:29:53Z
12902,"Regarding ""Hold types""",hold,urgent,10713483100563,2022-12-20T09:53:37Z
12934,"Satellogic - Add Plan No, Plan Rev and BOM Rev to ""Find Work Orders by Serial/Lot"" dispatch",hold,normal,1014712557,2022-12-22T21:02:44Z
12935,"Satellogic (Win Client) - Add a Data Collection Summary tab to the ""Unit Info"" Page",hold,normal,1014712557,2022-12-22T21:26:38Z
12959,Diff Check is missing parts!,hold,low,365557410973,2023-01-03T18:40:52Z
12986,WORK ORDER STATUS in REPLY OF S_WO_REQUEST,hold,low,5939008713619,2023-01-05T15:47:08Z
12988,Cannot create variable name starts with number under Data collection Variable - G8R2SP7HF2,open,low,379562026194,2023-01-05T18:46:16Z
13037,Within an NCR: Open different file types after each other,pending,normal,10713483100563,2023-01-13T11:48:16Z
13038,Case insensitive search,open,urgent,10713483100563,2023-01-13T13:01:27Z
13053,operations on work order is not in sequence,closed,high,5939008713619,2023-01-16T18:18:59Z
13061,Cannot access JS Report Studio with SSO enabled,closed,normal,1531036119081,2023-01-18T11:25:30Z
13065,Reopening Serialized Scrapped Units with open dispo orders,solved,low,395226442033,2023-01-18T13:15:24Z
13076,Question on ZD12590,hold,low,430101921733,2023-01-19T14:56:07Z
13092,Print multiple Unit Work History reports,hold,normal,433121299,2023-01-19T22:32:09Z
13113,Allow BOM and Process Plan build part to be a Standard Part,open,normal,433121299,2023-01-23T23:43:13Z
13117,"Cross Order option not showing ""Shop Order List"" for 2nd and Rest of Data Collections",hold,high,372965954574,2023-01-24T14:49:21Z
13145,Advanced dispatch filter switching OR to AND on refilter,hold,urgent,12297504381203,2023-01-26T16:02:40Z
13159,ScanActiion = saScanOnly properties on udv input field does not recognize barcode scanner input ,hold,high,963614658,2023-01-27T17:12:01Z
13160,"i080 - Work Order Note added via Web Client, are not visible in the Work Order on the Win client",closed,normal,401846981234,2023-01-27T18:28:28Z
13170,Prevent mismatch of Security Groups when authoring objects,open,high,433121299,2023-01-29T12:37:45Z
